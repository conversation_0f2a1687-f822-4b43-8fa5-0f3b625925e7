import os
import textwrap
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageOps, ImageEnhance
import random
import math
from datetime import datetime
from zhdate import ZhDate  # 导入中文农历日期库
import requests  # 用于API请求
import json  # 用于JSON数据处理

# API配置
API_URL = "https://dify.pg-code-go.com/v1/completion-messages"
API_KEY = "app-ZDE3Khy2p0FQa8MF4FeeaNjH"  # 在这里设置你的API密钥，或者通过环境变量设置


def get_sentence_from_api(api_key=None):
    """
    从API获取英文句子和翻译

    参数:
        api_key: API密钥，如果为None则使用全局配置或环境变量

    返回:
        tuple: (英文句子, 中文翻译) 或 None（如果API调用失败）
    """
    try:
        # 获取API密钥
        if api_key is None:
            api_key = API_KEY or os.getenv('DIFY_API_KEY')

        if not api_key:
            print("⚠️ 未设置API密钥，请设置API_KEY变量或DIFY_API_KEY环境变量")
            return None

        # API请求配置
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        data = {
            "inputs": {"query": "Hello, world!"},
            "response_mode": "blocking",
            "user": "pg-code"
        }

        print("🔄 正在从API获取英文句子和翻译...")

        # 发送POST请求
        response = requests.post(API_URL, headers=headers, json=data, timeout=10)
        response.raise_for_status()  # 检查HTTP错误

        # 解析JSON响应
        result = response.json()

        # 提取answer字段
        if "answer" in result:
            answer = result["answer"].strip()
            print(f"✅ API响应: {answer}")

            # 解析英文和翻译
            # 预期格式: "英文：xxx  \n翻译：yyy"
            lines = answer.split('\n')
            english_line = None
            chinese_line = None

            for line in lines:
                line = line.strip()
                if line.startswith("英文："):
                    english_line = line
                elif line.startswith("翻译："):
                    chinese_line = line

            if english_line and chinese_line:
                english = english_line.replace("英文：", "").strip()
                chinese = chinese_line.replace("翻译：", "").strip()

                if english and chinese:
                    print(f"✅ 成功解析 - 英文: {english[:50]}...")
                    print(f"✅ 成功解析 - 翻译: {chinese[:50]}...")
                    return english, chinese
                else:
                    print("⚠️ 解析后的英文或翻译为空")
                    return None
            else:
                print(f"⚠️ 无法从API响应中找到英文和翻译: {answer}")
                return None
        else:
            print(f"⚠️ API响应中没有answer字段: {result}")
            return None

    except requests.exceptions.Timeout:
        print("⚠️ API请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"⚠️ API请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"⚠️ JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"⚠️ 获取句子时出现未知错误: {e}")
        return None


def get_backup_sentence():
    """
    获取备用句子（当API不可用时使用）

    返回:
        tuple: (英文句子, 中文翻译)
    """
    backup_sentences = [
        (
            "It is no easy task to identify the reasons for this phenomenon which involves several complicated factors.",
            "要找出这一现象的原因并非易事，因为它涉及若干复杂的因素。"
        ),
        (
            "The rapid development of artificial intelligence has brought about tremendous changes in various fields.",
            "人工智能的快速发展给各个领域带来了巨大的变化。"
        ),
        (
            "Success is not final, failure is not fatal: it is the courage to continue that counts.",
            "成功不是终点，失败不是致命的：重要的是继续前进的勇气。"
        ),
        (
            "While some people believe that technology will solve all our problems, others argue that it may create new challenges.",
            "虽然有些人认为技术将解决我们所有的问题，但其他人则认为，技术可能会创造出新的挑战。"
        ),
        (
            "Education is the most powerful weapon which you can use to change the world.",
            "教育是你可以用来改变世界的最强大的武器。"
        )
    ]

    selected = random.choice(backup_sentences)
    print(f"📝 使用备用句子: {selected[0][:50]}...")
    return selected


def generate_sentence_image(sentence_data=None, output_path="sentence_image.png",
                            bg_color=None, font_size_en=42, font_size_cn=32,
                            force_punctuation_wrap=True):
    """
    生成精美的英语句子图片

    参数:
        sentence_data: 包含英文句子和翻译的列表，格式为["英文：xxx", "翻译：yyy"]，
                      如果为None则从API获取
        output_path: 输出图片路径
        bg_color: 背景颜色，如果为None则随机选择
        font_size_en: 英文字体大小
        font_size_cn: 中文字体大小
        force_punctuation_wrap: 是否强制在标点符号处换行
    """
    # 如果没有提供句子数据，则从API获取
    if sentence_data is None:
        api_result = get_sentence_from_api()
        if api_result:
            english, chinese = api_result
        else:
            # API失败时使用备用句子
            english, chinese = get_backup_sentence()
    else:
        # 使用提供的句子数据
        if isinstance(sentence_data, (list, tuple)) and len(sentence_data) >= 2:
            english = sentence_data[0].replace("英文：", "").strip()
            chinese = sentence_data[1].replace("翻译：", "").strip()
        else:
            print("⚠️ 句子数据格式错误，使用备用句子")
            english, chinese = get_backup_sentence()

    # 设置图片尺寸 - 调整为更合适的比例
    width, height = 900, 1400

    # 如果没有指定背景颜色，使用更精致的现代色彩方案
    if bg_color is None:
        # 使用更现代和优雅的色彩搭配，提升视觉体验
        bg_colors = [
            (220, 85, 85),   # 优雅红色 - 主推荐，更加柔和温暖
            (255, 107, 107), # 珊瑚红 - 温暖活泼
            (255, 99, 132),  # 玫瑰红 - 优雅浪漫
            (255, 87, 51),   # 橙红色 - 活力四射
            (255, 138, 101), # 桃红色 - 温馨柔和
            (220, 38, 127),  # 深粉红 - 时尚现代
            (156, 39, 176),  # 紫红色 - 神秘优雅
            (103, 58, 183),  # 深紫色 - 沉稳大气
            (67, 160, 71),   # 现代绿色 - 清新自然
            (33, 150, 243),  # 现代蓝色 - 专业稳重
        ]
        # 默认使用最优雅的现代红色，也可以随机选择
        bg_color = bg_colors[0]  # 或者使用 random.choice(bg_colors)

    # 创建现代化的渐变背景 - 多重渐变效果
    def create_modern_gradient(width, height, base_color):
        """创建现代化的多重渐变背景"""
        r, g, b = base_color

        # 创建基础图像
        image = Image.new('RGB', (width, height), base_color)

        # 创建更复杂的渐变效果 - 结合线性和径向渐变
        for y in range(height):
            for x in range(width):
                # 线性渐变（从上到下）
                linear_ratio = y / height
                linear_factor = 1 - (linear_ratio * 0.12)  # 减少渐变强度，更柔和

                # 径向渐变（从中心向外）
                center_x, center_y = width // 2, height // 4  # 调整中心位置
                distance = math.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)
                max_distance = math.sqrt(width ** 2 + height ** 2) / 3.0  # 调整渐变范围
                radial_ratio = min(distance / max_distance, 1.0)
                radial_factor = 1 - (radial_ratio * 0.15)  # 减少径向渐变强度

                # 结合两种渐变效果
                combined_factor = (linear_factor + radial_factor) / 2

                # 添加微妙的色彩变化 - 更加细腻
                hue_shift = math.sin(x / width * math.pi * 2) * 0.03  # 减少色相变化
                saturation_shift = math.cos(y / height * math.pi) * 0.02  # 添加饱和度变化

                # 计算最终颜色
                final_r = int(r * combined_factor * (1 + hue_shift))
                final_g = int(g * combined_factor * (1 + saturation_shift))
                final_b = int(b * combined_factor * (1 - hue_shift * 0.3))

                # 确保颜色值在有效范围内
                final_r = max(0, min(255, final_r))
                final_g = max(0, min(255, final_g))
                final_b = max(0, min(255, final_b))

                image.putpixel((x, y), (final_r, final_g, final_b))

        return image

    # 创建现代化的渐变背景
    image = create_modern_gradient(width, height, bg_color)

    # 添加轻微的纹理效果，使背景更加精致
    def add_subtle_texture(image, opacity=0.05):
        """添加轻微的纹理效果"""
        width, height = image.size
        texture = Image.new('RGB', (width, height), (255, 255, 255))
        draw_texture = ImageDraw.Draw(texture)

        # 添加随机小点
        for _ in range(width * height // 100):
            x = random.randint(0, width - 1)
            y = random.randint(0, height - 1)
            radius = random.randint(1, 3)
            color_value = random.randint(200, 255)
            draw_texture.ellipse([(x - radius, y - radius), (x + radius, y + radius)],
                                 fill=(color_value, color_value, color_value))

        # 将纹理与原图混合
        texture = texture.convert('RGBA')
        texture_layer = Image.new('RGBA', (width, height), (255, 255, 255, 0))
        texture_layer.paste(texture, (0, 0))

        # 调整透明度
        alpha = Image.new('RGBA', texture_layer.size, (255, 255, 255, int(255 * opacity)))
        texture_layer = Image.composite(texture_layer, Image.new('RGBA', texture_layer.size, (0, 0, 0, 0)), alpha)

        # 转换原图为RGBA
        image_rgba = image.convert('RGBA')

        # 混合图层
        result = Image.alpha_composite(image_rgba, texture_layer)
        return result.convert('RGB')

    # 添加轻微的纹理效果
    image = add_subtle_texture(image, opacity=0.03)

    # 添加现代化的顶部和底部装饰条
    bar_height = 20

    # 创建现代化的装饰条 - 使用渐变和阴影效果
    def create_modern_bar(width, height, is_top=True):
        bar = Image.new('RGB', (width, height), (0, 0, 0, 0))

        # 创建渐变效果
        for y in range(height):
            ratio = y / height if is_top else (height - y) / height

            # 使用更现代的深色调
            base_gray = 45
            current_gray = int(base_gray + ratio * 15)  # 从深到稍浅的渐变
            color = (current_gray, current_gray, current_gray)

            for x in range(width):
                bar.putpixel((x, y), color)

        # 添加微妙的高光线条
        draw_bar = ImageDraw.Draw(bar)
        if is_top:
            # 顶部条的底边高光
            draw_bar.line([(0, height - 1), (width, height - 1)], fill=(80, 80, 80), width=1)
        else:
            # 底部条的顶边高光
            draw_bar.line([(0, 0), (width, 0)], fill=(80, 80, 80), width=1)

        return bar

    top_bar = create_modern_bar(width, bar_height, True)
    bottom_bar = create_modern_bar(width, bar_height, False)
    image.paste(top_bar, (0, 0))
    image.paste(bottom_bar, (0, height - bar_height))

    # 创建绘图对象
    draw = ImageDraw.Draw(image)

    # 尝试加载字体，如果找不到则使用默认字体
    try:
        # 尝试加载常见的英文和中文字体
        font_paths = [
            # Windows 字体路径
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/arialbd.ttf",  # Arial Bold
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/simkai.ttf",
            # Mac 字体路径
            "/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/PingFang.ttc",
            # Linux 字体路径
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
        ]

        # 尝试找到可用的英文和中文字体
        en_font = None
        en_bold_font = None
        cn_font = None
        cn_bold_font = None

        for path in font_paths:
            if os.path.exists(path):
                if "arial" in path.lower() or "dejavu" in path.lower():
                    if "bold" in path.lower() or "bd" in path.lower():
                        en_bold_font = ImageFont.truetype(path, font_size_en + 10)
                    else:
                        en_font = ImageFont.truetype(path, font_size_en)
                if "simhei" in path.lower() or "msyh" in path.lower() or "pingfang" in path.lower() or "wqy" in path.lower():
                    cn_font = ImageFont.truetype(path, font_size_cn)
                    cn_bold_font = ImageFont.truetype(path, font_size_cn + 20)

        # 如果找不到特定字体，使用默认字体
        if not en_font:
            en_font = ImageFont.load_default()
            font_size_en = 20
        if not en_bold_font:
            en_bold_font = en_font
        if not cn_font:
            cn_font = ImageFont.load_default()
            font_size_cn = 18
        if not cn_bold_font:
            cn_bold_font = cn_font

    except Exception as e:
        print(f"加载字体时出错: {e}")
        en_font = ImageFont.load_default()
        en_bold_font = ImageFont.load_default()
        cn_font = ImageFont.load_default()
        cn_bold_font = ImageFont.load_default()
        font_size_en = 20
        font_size_cn = 18

    # 获取当前日期
    now = datetime.now()
    day_mapping = {
        0: ("MONDAY", "星期一"),
        1: ("TUESDAY", "星期二"),
        2: ("WEDNESDAY", "星期三"),
        3: ("THURSDAY", "星期四"),
        4: ("FRIDAY", "星期五"),
        5: ("SATURDAY", "星期六"),
        6: ("SUNDAY", "星期日")
    }

    # 使用当前日期
    day_of_week, chinese_day = day_mapping[now.weekday()]
    # 不再使用固定的星期一
    # day_of_week, chinese_day = day_mapping[0]  # 固定使用星期一

    # 绘制顶部内容区域（红色背景区域）- 增加高度以提供更好的视觉平衡
    top_content_height = 320

    # 添加现代化的文字效果函数
    def draw_text_with_modern_effect(draw, position, text, font, fill_color, glow_intensity=0.8, shadow_color=(0, 0, 0)):
        x, y = position

        # 首先绘制阴影效果 - 更自然的阴影
        shadow_offsets = [(2, 2), (1, 1)]
        for i, (shadow_x, shadow_y) in enumerate(shadow_offsets):
            shadow_alpha = 30 - i * 10  # 渐变阴影
            try:
                shadow_rgba = (*shadow_color, shadow_alpha)
                draw.text((x + shadow_x, y + shadow_y), text, fill=shadow_rgba, font=font)
            except:
                # 如果不支持透明度，使用普通颜色
                shadow_gray = tuple(max(0, c - 50) for c in fill_color[:3])
                draw.text((x + shadow_x, y + shadow_y), text, fill=shadow_gray, font=font)

        # 创建现代化的发光效果 - 使用更简洁的方法
        glow_offsets = [
            (-1, -1), (0, -1), (1, -1),
            (-1, 0), (1, 0),
            (-1, 1), (0, 1), (1, 1),
        ]

        # 绘制发光效果
        for offset_x, offset_y in glow_offsets:
            # 计算距离来调整透明度
            distance = math.sqrt(offset_x ** 2 + offset_y ** 2)
            alpha = max(8, int(25 * glow_intensity / (distance + 1)))
            try:
                current_glow = (255, 255, 255, alpha)
                draw.text((x + offset_x, y + offset_y), text, fill=current_glow, font=font)
            except:
                # 如果不支持透明度，使用普通颜色
                glow_gray = tuple(min(255, c + 20) for c in fill_color[:3])
                draw.text((x + offset_x, y + offset_y), text, fill=glow_gray, font=font)

        # 绘制主文字
        draw.text((x, y), text, fill=fill_color, font=font)

    # 绘制英文星期几 - 居中，添加现代发光效果，调整位置
    day_en_width = draw.textlength(day_of_week, font=en_bold_font) if hasattr(draw, 'textlength') else len(
        day_of_week) * (font_size_en + 10) // 2
    draw_text_with_modern_effect(draw, ((width - day_en_width) // 2, 70),
                                 day_of_week, en_bold_font, (255, 255, 255), 1.0)

    # 绘制中文星期几 - 居中，使用更大的字体，添加发光效果，调整位置
    day_cn_width = draw.textlength(chinese_day, font=cn_bold_font) if hasattr(draw, 'textlength') else len(
        chinese_day) * (font_size_cn + 20)
    draw_text_with_modern_effect(draw, ((width - day_cn_width) // 2, 140),
                                 chinese_day, cn_bold_font, (255, 255, 255), 1.0)

    # 添加更优雅的诗句或格言，使用更小的字体和更精致的位置
    # 从API获取格言
    def get_quote_from_api():
        """从API获取格言，如果失败则使用备用格言"""
        try:
            # 设置请求超时时间
            response = requests.get("http://dwz.2xb.cn/RainbowWord", timeout=5)
            response.raise_for_status()  # 检查HTTP错误

            # 解析JSON响应
            quote_text = response.text.strip()

            # 去除可能的引号
            if quote_text.startswith('"') and quote_text.endswith('"'):
                quote_text = quote_text[1:-1]

            # 验证格言长度和内容
            if quote_text and len(quote_text) <= 50 and len(quote_text) >= 4:
                print(f"✅ 成功获取API格言: {quote_text}")
                return quote_text
            else:
                print(f"⚠️ API返回的格言格式不符合要求: {quote_text}")
                return None

        except requests.exceptions.Timeout:
            print("⚠️ API请求超时，使用备用格言")
            return None
        except requests.exceptions.RequestException as e:
            print(f"⚠️ API请求失败: {e}，使用备用格言")
            return None
        except Exception as e:
            print(f"⚠️ 获取格言时出现未知错误: {e}，使用备用格言")
            return None

    # 备用格言库（当API不可用时使用）
    backup_quotes = [
        "浮云吹作雪，世味煮成茶",
        "人生如逆旅，我亦是行人",
        "不积跬步，无以至千里",
        "学而时习之，不亦说乎",
        "千里之行，始于足下",
        "锲而不舍，金石可镂",
        "天行健，君子以自强不息"
    ]

    # 尝试从API获取格言，失败则使用备用格言
    quote = get_quote_from_api()
    if quote is None:
        quote = random.choice(backup_quotes)
        print(f"📝 使用备用格言: {quote}")
    quote_width = draw.textlength(quote, font=cn_font) if hasattr(draw, 'textlength') else len(quote) * font_size_cn
    # 使用现代化的发光效果，营造更优雅的氛围，调整位置
    draw_text_with_modern_effect(draw, ((width - quote_width) // 2, 220),
                                 quote, cn_font, (255, 255, 255), 0.6)

    # 绘制更优雅的NEWS标签区域
    news_text = "NEWS"
    news_width = draw.textlength(news_text, font=en_font) if hasattr(draw, 'textlength') else len(
        news_text) * font_size_en // 2

    # 创建更优雅的分隔线 - 使用渐变效果
    def draw_elegant_line(draw, start_pos, end_pos, color, width=1):
        x1, y1 = start_pos
        x2, y2 = end_pos

        # 绘制主线条
        draw.line([(x1, y1), (x2, y2)], fill=color, width=width)

        # 添加轻微的高光效果
        highlight_color = tuple(min(255, c + 30) for c in color)
        draw.line([(x1, y1 - 1), (x2, y2 - 1)], fill=highlight_color, width=1)

    # 在顶部内容区域和白色内容区域之间绘制优雅分隔线
    draw_elegant_line(draw, (50, top_content_height), (width - 50, top_content_height), (120, 120, 120), 2)

    # 绘制NEWS标签 - 使用更优雅的颜色和位置
    draw.text(((width - news_width) // 2, top_content_height + 25),
              news_text, fill=(180, 180, 180), font=en_font)

    # 绘制下方优雅分隔线
    draw_elegant_line(draw, (50, top_content_height + 70), (width - 50, top_content_height + 70), (120, 120, 120), 2)

    # 绘制更优雅的白色内容区域背景 - 添加微妙的渐变和纹理
    white_bg_height = height - top_content_height - bar_height

    # 创建带有微妙渐变的白色背景
    def create_elegant_white_background(width, height):
        bg = Image.new('RGB', (width, height), (255, 255, 255))

        # 添加从上到下的微妙渐变（从纯白到微微的灰白）
        for y in range(height):
            ratio = y / height
            # 非常微妙的渐变，增加一些温暖感
            gray_value = int(255 - ratio * 6)  # 减少变暗程度，更柔和
            # 添加微妙的暖色调
            warm_r = int(255 - ratio * 4)
            warm_g = int(255 - ratio * 5)
            warm_b = int(255 - ratio * 7)
            color = (warm_r, warm_g, warm_b)

            for x in range(width):
                bg.putpixel((x, y), color)

        return bg

    white_bg = create_elegant_white_background(width, white_bg_height)

    # 添加更精致的纸张纹理效果
    draw_bg = ImageDraw.Draw(white_bg)
    for i in range(0, white_bg_height, 80):  # 进一步增加间距，使纹理更加精致
        # 添加非常淡的水平线，模拟高质量纸张纹理
        line_alpha = max(0.3, 1 - i / white_bg_height)  # 渐变透明度
        line_color = (int(250 - i / white_bg_height * 5), int(250 - i / white_bg_height * 5), int(250 - i / white_bg_height * 5))
        draw_bg.line([(40, i), (width - 40, i)], fill=line_color, width=1)

    image.paste(white_bg, (0, top_content_height))

    # 重新创建绘图对象（因为我们修改了图像）
    draw = ImageDraw.Draw(image)

    # 获取当前日期信息
    now = datetime.now()
    current_date = now.strftime("%Y年%m月%d日")

    # 获取农历日期 - 修正格式
    try:
        lunar = ZhDate.from_datetime(now)
        lunar_month = lunar.lunar_month
        lunar_day = lunar.lunar_day

        # 农历月份和日期的中文表示
        lunar_month_names = ["正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"]
        lunar_day_names = ["初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
                           "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
                           "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十"]

        lunar_month_str = lunar_month_names[lunar_month - 1] + "月"
        lunar_day_str = lunar_day_names[lunar_day - 1]

        lunar_date = f"农历{lunar_month_str}{lunar_day_str}"
    except Exception as e:
        print(f"农历转换出错: {e}")
        lunar_date = "农历未知"

    # 绘制日期信息
    date_font_size = font_size_cn - 5
    date_font = cn_font

    # 左侧农历信息
    draw.text((50, top_content_height + 80), lunar_date, fill=(100, 100, 100), font=date_font)

    # 右侧公历信息
    date_width = draw.textlength(current_date, font=date_font) if hasattr(draw, 'textlength') else len(
        current_date) * date_font_size // 2
    draw.text((width - 50 - date_width, top_content_height + 80), current_date, fill=(100, 100, 100), font=date_font)

    # 绘制中间的简报标题 - 使用更优雅的红色
    news_title = "每日金句"
    news_title_width = draw.textlength(news_title, font=cn_font) if hasattr(draw, 'textlength') else len(
        news_title) * font_size_cn
    # 使用更优雅的红色，与顶部背景色协调
    elegant_red = (220, 50, 50)

    # 为标题添加更精致的背景和装饰
    title_padding = 10
    title_height = font_size_cn + 2 * title_padding
    title_width = news_title_width + 4 * title_padding
    title_x = (width - title_width) // 2
    title_y = top_content_height + 75

    # 绘制标题背景
    draw.rectangle([(title_x, title_y), (title_x + title_width, title_y + title_height)],
                   fill=(255, 255, 255), outline=elegant_red, width=1)

    # 添加内部装饰线
    draw.rectangle([(title_x + 2, title_y + 2), (title_x + title_width - 2, title_y + title_height - 2)],
                   fill=None, outline=elegant_red, width=1)

    # 绘制标题文本
    draw.text(((width - news_title_width) // 2, title_y + title_padding),
              news_title, fill=elegant_red, font=cn_font)

    # 绘制英文句子区域 - 添加更精致的背景框和装饰，增加间距
    sentence_y = top_content_height + 180 + title_height  # 调整句子区域位置，增加更多间距

    # 创建现代化的句子区域背景框
    def create_modern_sentence_background(draw, x, y, width, height):
        # 创建现代化的多层阴影效果
        shadow_layers = [
            ((x + 6, y + 6), (220, 220, 220, 40)),  # 最外层阴影
            ((x + 4, y + 4), (230, 230, 230, 60)),  # 中层阴影
            ((x + 2, y + 2), (240, 240, 240, 80)),  # 内层阴影
        ]

        for (shadow_x, shadow_y), shadow_color in shadow_layers:
            try:
                # 尝试使用带透明度的颜色
                draw.rectangle([(shadow_x, shadow_y), (shadow_x + width, shadow_y + height)],
                               fill=shadow_color, outline=None)
            except:
                # 如果不支持透明度，使用普通颜色
                simple_color = shadow_color[:3]
                draw.rectangle([(shadow_x, shadow_y), (shadow_x + width, shadow_y + height)],
                               fill=simple_color, outline=None)

        # 绘制主背景 - 使用现代化的白色和圆角效果
        bg_color = (254, 254, 252)  # 更纯净的白色
        border_color = (180, 180, 180)  # 更柔和的边框色

        # 主背景矩形
        draw.rectangle([(x, y), (x + width, y + height)], fill=bg_color, outline=border_color, width=2)

        # 添加内部高光边框 - 更精致的效果
        highlight_color = (255, 255, 255)
        draw.rectangle([(x + 2, y + 2), (x + width - 2, y + height - 2)],
                       fill=None, outline=highlight_color, width=1)

        # 添加装饰性的角落元素
        corner_size = 8
        accent_color = bg_color  # 使用背景色的变体

        # 四个角落的小装饰
        corners = [
            (x, y),  # 左上
            (x + width - corner_size, y),  # 右上
            (x, y + height - corner_size),  # 左下
            (x + width - corner_size, y + height - corner_size),  # 右下
        ]

        for corner_x, corner_y in corners:
            draw.rectangle([(corner_x, corner_y), (corner_x + corner_size, corner_y + corner_size)],
                           fill=accent_color, outline=None)

    # 计算句子区域的大小
    # 增加英文每行字符数，减少行数
    wrapped_english = textwrap.fill(english, width=40)
    en_lines = wrapped_english.split('\n')

    # 优化中文换行，确保适当的行长度和断句
    # 对中文进行更智能的换行处理
    def smart_chinese_wrap(text, width, force_punctuation_wrap=True):
        """
        对中文文本进行智能换行，优先在中文标点符号处换行

        参数:
            text: 要换行的中文文本
            width: 每行的最大字符数
            force_punctuation_wrap: 是否强制在标点符号处换行

        返回:
            换行后的文本
        """
        # 中文标点符号列表，优先在这些符号后换行
        punctuations = "，。、；：！？.,"

        # 如果文本长度小于宽度，直接返回
        if len(text) <= width:
            return text

        result_lines = []
        current_line = ""

        # 遍历文本中的每个字符
        i = 0
        while i < len(text):
            char = text[i]
            current_line += char

            # 如果启用了强制标点换行，且当前字符是标点符号，且当前行长度超过宽度的50%，则在此处换行
            if force_punctuation_wrap and char in punctuations and len(current_line) >= width * 0.5:
                result_lines.append(current_line)
                current_line = ""
            # 如果当前行已达到最大宽度，查找最近的标点符号
            elif len(current_line) >= width:
                # 检查当前行中是否有标点符号，如果有，在最后一个标点符号后换行
                last_punct_index = -1
                # 向前查找最多12个字符，寻找标点符号
                for j in range(len(current_line) - 1, max(0, len(current_line) - 12), -1):
                    if current_line[j] in punctuations:
                        last_punct_index = j
                        break

                if last_punct_index >= 0:
                    # 在标点符号后换行
                    result_lines.append(current_line[:last_punct_index + 1])
                    current_line = current_line[last_punct_index + 1:]
                else:
                    # 没有找到标点符号，强制换行
                    result_lines.append(current_line)
                    current_line = ""

            i += 1

        # 添加最后一行
        if current_line:
            result_lines.append(current_line)

        return '\n'.join(result_lines)

    # 使用智能中文换行，增加宽度参数以减少过度换行
    wrapped_chinese = smart_chinese_wrap(chinese, width=22, force_punctuation_wrap=force_punctuation_wrap)
    cn_lines = wrapped_chinese.split('\n')

    # 计算所需的高度 - 去除标签后调整高度计算
    # 英文部分高度：行数 * (字体大小 + 行间距)
    en_height = len(en_lines) * (font_size_en + 14)
    # 中文部分高度：行数 * (字体大小 + 行间距)
    cn_height = len(cn_lines) * (font_size_cn + 12)
    # 分隔线和边距高度
    divider_height = 60  # 分隔线区域高度
    padding_height = 50  # 上下边距

    total_height = en_height + cn_height + divider_height + padding_height
    sentence_bg_width = width - 120  # 增加左右边距以提供更好的视觉平衡
    sentence_bg_x = 60

    # 绘制现代化句子背景框
    create_modern_sentence_background(draw, sentence_bg_x, sentence_y - 25, sentence_bg_width, total_height)

    # 添加现代化装饰元素
    def draw_modern_decoration(draw, x, y, width, height):
        """在句子框周围添加现代化装饰元素"""
        accent_color = bg_color  # 使用主题色作为装饰色
        light_accent = tuple(min(255, c + 30) for c in accent_color[:3])

        # 添加顶部装饰线条
        line_width = width // 4
        line_x = x + (width - line_width) // 2
        draw.rectangle([(line_x, y - 8), (line_x + line_width, y - 6)],
                       fill=light_accent, outline=None)

        # 添加底部装饰线条
        draw.rectangle([(line_x, y + height + 6), (line_x + line_width, y + height + 8)],
                       fill=light_accent, outline=None)

        # 添加左右两侧的小装饰点
        dot_size = 4
        dot_y_positions = [y + height // 4, y + height // 2, y + 3 * height // 4]

        for dot_y in dot_y_positions:
            # 左侧装饰点
            draw.ellipse([(x - 12, dot_y - dot_size // 2), (x - 12 + dot_size, dot_y + dot_size // 2)],
                         fill=light_accent, outline=None)
            # 右侧装饰点
            draw.ellipse([(x + width + 8, dot_y - dot_size // 2), (x + width + 8 + dot_size, dot_y + dot_size // 2)],
                         fill=light_accent, outline=None)

    # 添加现代化装饰
    draw_modern_decoration(draw, sentence_bg_x, sentence_y - 25, sentence_bg_width, total_height)

    # 添加简洁优雅的装饰分割线
    def draw_decorative_divider(draw, x, y, width, color=(160, 160, 160)):
        """绘制简洁优雅的装饰性分割线"""
        # 绘制主分割线 - 更简洁的设计
        line_width = width // 3  # 缩短线条长度
        line_x = x + (width - line_width) // 2

        # 绘制渐变效果的主线
        line_segments = 12
        segment_width = line_width // line_segments

        for i in range(line_segments):
            segment_x = line_x + i * segment_width
            # 创建从中心向外的渐变效果
            distance_from_center = abs(i - line_segments // 2)
            alpha_factor = 1 - (distance_from_center / (line_segments // 2)) * 0.6
            current_color = tuple(int(c * alpha_factor) for c in color)
            draw.line([(segment_x, y), (segment_x + segment_width, y)], fill=current_color, width=1)

        # 中间简洁的装饰点
        mid_x = x + width // 2
        dot_radius = 2
        draw.ellipse([(mid_x - dot_radius, y - dot_radius),
                      (mid_x + dot_radius, y + dot_radius)],
                     fill=color, outline=None)

        # 左右两侧的小装饰点 - 更小更精致
        left_x = line_x - 15
        right_x = line_x + line_width + 15
        small_radius = 1

        # 左侧装饰点
        draw.ellipse([(left_x - small_radius, y - small_radius),
                      (left_x + small_radius, y + small_radius)],
                     fill=tuple(int(c * 0.7) for c in color), outline=None)

        # 右侧装饰点
        draw.ellipse([(right_x - small_radius, y - small_radius),
                      (right_x + small_radius, y + small_radius)],
                     fill=tuple(int(c * 0.7) for c in color), outline=None)

    # 绘制英文句子（自动换行）- 使用更大的字体和更好的间距
    sentence_y += 25  # 在背景框内留出适当边距，去除标签后减少间距

    # 绘制英文句子，使用更优雅的颜色和间距
    for line in en_lines:
        line_width = draw.textlength(line, font=en_font) if hasattr(draw, 'textlength') else len(line) * (
                    font_size_en // 2)
        draw.text(((width - line_width) // 2, sentence_y),
                  line, fill=(45, 45, 45), font=en_font)  # 使用深灰色，更优雅
        sentence_y += font_size_en + 14  # 适当增加行间距

    # 在英文和中文之间添加装饰分割线 - 更简洁的设计
    divider_width = sentence_bg_width - 120
    divider_x = sentence_bg_x + 60
    divider_y = sentence_y + 20
    draw_decorative_divider(draw, divider_x, divider_y, divider_width, (160, 160, 160))
    sentence_y += 40  # 分隔线后适当间距

    # 绘制中文翻译（自动换行）- 调整行间距和字体大小
    for line in cn_lines:
        line_width = draw.textlength(line, font=cn_font) if hasattr(draw, 'textlength') else len(line) * (
                    font_size_cn // 2)
        draw.text(((width - line_width) // 2, sentence_y),
                  line, fill=(65, 65, 65), font=cn_font)  # 使用深灰色，与英文保持一致的风格
        sentence_y += font_size_cn + 12  # 适当增加中文行间距

    # 应用轻微的图像增强
    def enhance_image(image):
        """应用轻微的图像增强效果"""
        # 轻微增加对比度
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.08)  # 稍微增加对比度

        # 轻微增加清晰度
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.15)  # 增加清晰度

        # 轻微增加亮度
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(1.03)  # 稍微增加亮度

        # 轻微增加色彩饱和度
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.05)  # 增加色彩饱和度

        return image

    # 应用图像增强
    image = enhance_image(image)

    # 保存图片
    image.save(output_path)
    print(f"图片已保存至: {output_path}")
    return output_path


def test_api_connection(api_key=None):
    """
    测试API连接

    参数:
        api_key: API密钥

    返回:
        bool: 连接是否成功
    """
    print("🔍 测试API连接...")
    result = get_sentence_from_api(api_key)
    if result:
        english, chinese = result
        print(f"✅ API连接成功！")
        print(f"📝 英文: {english[:50]}...")
        print(f"📝 翻译: {chinese[:50]}...")
        return True
    else:
        print("❌ API连接失败")
        return False


# 示例使用
if __name__ == "__main__":
    print("🎨 开始生成英语句子图片...")
    print("=" * 50)

    # 检查API配置
    print("\n🔧 检查API配置...")
    if not API_KEY and not os.getenv('DIFY_API_KEY'):
        print("⚠️ 未设置API密钥")
        print("💡 请在代码中设置API_KEY变量或设置DIFY_API_KEY环境变量")
        print("💡 示例: API_KEY = 'app-your-api-key-here'")
        print("💡 或者: set DIFY_API_KEY=app-your-api-key-here")
        print("\n🔄 将使用备用句子继续生成图片...")
    else:
        # 测试API连接
        test_api_connection()

    # 主要用例：从API获取句子并生成图片
    print("\n📡 从API获取句子并生成图片...")
    generate_sentence_image(output_path="api_sentence_image.png")

    # 测试用例：使用不同主题色彩
    print("\n🎨 生成不同主题的图片...")

    # 绿色主题
    print("🟢 生成绿色主题图片...")
    generate_sentence_image(output_path="api_sentence_green.png", bg_color=(67, 160, 71))

    # 蓝色主题
    print("🔵 生成蓝色主题图片...")
    generate_sentence_image(output_path="api_sentence_blue.png", bg_color=(33, 150, 243))

    # 紫色主题
    print("🟣 生成紫色主题图片...")
    generate_sentence_image(output_path="api_sentence_purple.png", bg_color=(156, 39, 176))

    # 橙色主题
    print("🟠 生成橙色主题图片...")
    generate_sentence_image(output_path="api_sentence_orange.png", bg_color=(255, 87, 51))

    print("\n" + "=" * 50)
    print("✅ 所有图片生成完成！")
    print("\n📁 生成的图片文件：")
    print("- api_sentence_image.png (默认红色主题)")
    print("- api_sentence_green.png (绿色主题)")
    print("- api_sentence_blue.png (蓝色主题)")
    print("- api_sentence_purple.png (紫色主题)")
    print("- api_sentence_orange.png (橙色主题)")

    print("\n🚀 主要功能：")
    print("- ✨ 自动从API获取英文句子和翻译")
    print("- 🎨 支持多种主题色彩")
    print("- 📱 现代化的图片设计")
    print("- 🔄 API失败时自动使用备用句子")
    print("- 📅 显示当前日期和农历信息")
    print("- 💫 优雅的渐变背景和装饰效果")
