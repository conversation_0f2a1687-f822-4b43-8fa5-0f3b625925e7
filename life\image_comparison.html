<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语句子图片优化对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .comparison-section {
            margin-bottom: 50px;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.5em;
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
            padding: 10px;
            background: linear-gradient(45deg, #ffeaa7, #fab1a0);
            border-radius: 8px;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .image-card:hover {
            transform: translateY(-5px);
        }
        
        .image-card img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        
        .image-title {
            text-align: center;
            margin: 15px 0 10px 0;
            font-weight: bold;
            color: #2d3436;
            font-size: 1.1em;
        }
        
        .improvements {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .improvements h2 {
            margin-top: 0;
            font-size: 1.8em;
            text-align: center;
        }
        
        .improvements ul {
            list-style: none;
            padding: 0;
        }
        
        .improvements li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .improvements li:before {
            content: "✨";
            position: absolute;
            left: 0;
            top: 8px;
        }
        
        .color-themes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .theme-card {
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .theme-card img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        
        .theme-name {
            text-align: center;
            margin: 10px 0 5px 0;
            font-weight: bold;
            color: #2d3436;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>英语句子图片生成器 - 优化对比</h1>
        
        <div class="comparison-section">
            <div class="section-title">优化前后对比</div>
            <div class="image-grid">
                <div class="image-card">
                    <div class="image-title">优化前</div>
                    <img src="sentence_image1.png" alt="优化前的图片">
                </div>
                <div class="image-card">
                    <div class="image-title">优化后</div>
                    <img src="optimized_sentence_image1.png" alt="优化后的图片">
                </div>
            </div>
        </div>
        
        <div class="comparison-section">
            <div class="section-title">多种颜色主题展示</div>
            <div class="color-themes">
                <div class="theme-card">
                    <img src="optimized_sentence_image1.png" alt="红色主题">
                    <div class="theme-name">优雅红色主题</div>
                </div>
                <div class="theme-card">
                    <img src="optimized_sentence_image2.png" alt="绿色主题">
                    <div class="theme-name">清新绿色主题</div>
                </div>
                <div class="theme-card">
                    <img src="optimized_sentence_image3.png" alt="蓝色主题">
                    <div class="theme-name">专业蓝色主题</div>
                </div>
                <div class="theme-card">
                    <img src="optimized_sentence_image4.png" alt="紫色主题">
                    <div class="theme-name">神秘紫色主题</div>
                </div>
            </div>
        </div>
        
        <div class="improvements">
            <h2>主要优化改进</h2>
            <ul>
                <li><strong>背景渐变优化：</strong>调整了渐变强度和色彩变化，使背景更加柔和自然</li>
                <li><strong>色彩方案扩展：</strong>新增绿色和蓝色主题，提供更多视觉选择</li>
                <li><strong>文字效果增强：</strong>改进了阴影和发光效果，提升文字的立体感和可读性</li>
                <li><strong>纸张纹理优化：</strong>调整了纹理间距和透明度，营造更精致的质感</li>
                <li><strong>白色背景改进：</strong>添加微妙的暖色调，使整体更加温馨</li>
                <li><strong>图像增强：</strong>提升了对比度、清晰度和色彩饱和度</li>
                <li><strong>格言库扩展：</strong>增加了更多励志和学习相关的古诗词名句</li>
                <li><strong>视觉平衡优化：</strong>调整了各元素的间距和比例，提升整体美感</li>
            </ul>
        </div>
    </div>
</body>
</html>
