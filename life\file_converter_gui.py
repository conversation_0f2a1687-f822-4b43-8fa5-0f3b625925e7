#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件格式转换器 - 可视化界面
支持多种文件格式之间的转换
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from pathlib import Path
import json
from PIL import Image, ImageTk
import pandas as pd
from docx import Document
import markdown
import csv


class FileConverter:
    """文件转换器核心类"""
    
    def __init__(self):
        self.supported_conversions = {
            # 图片格式转换
            'image': {
                'extensions': ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'],
                'target_formats': ['.jpg', '.png', '.bmp', '.gif', '.tiff', '.webp']
            },
            # 文档格式转换
            'document': {
                'extensions': ['.txt', '.md', '.docx', '.csv'],
                'target_formats': ['.txt', '.md', '.docx', '.csv', '.html']
            },
            # 数据格式转换
            'data': {
                'extensions': ['.csv', '.json', '.xlsx'],
                'target_formats': ['.csv', '.json', '.xlsx']
            }
        }
    
    def get_file_category(self, file_ext):
        """根据文件扩展名获取文件类别"""
        for category, info in self.supported_conversions.items():
            if file_ext.lower() in info['extensions']:
                return category
        return None
    
    def get_target_formats(self, source_ext):
        """获取源格式支持的目标格式"""
        category = self.get_file_category(source_ext)
        if category:
            return self.supported_conversions[category]['target_formats']
        return []
    
    def convert_image(self, source_path, target_path, target_format):
        """转换图片格式"""
        try:
            with Image.open(source_path) as img:
                # 处理RGBA模式到RGB的转换
                if target_format.lower() in ['.jpg', '.jpeg'] and img.mode in ['RGBA', 'P']:
                    img = img.convert('RGB')
                
                # 保存图片
                img.save(target_path, format=target_format[1:].upper())
            return True
        except Exception as e:
            print(f"图片转换错误: {e}")
            return False
    
    def convert_document(self, source_path, target_path, source_ext, target_ext):
        """转换文档格式"""
        try:
            if source_ext == '.txt' and target_ext == '.md':
                with open(source_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(target_path, 'w', encoding='utf-8') as f:
                    f.write(f"# 转换的文档\n\n{content}")
                    
            elif source_ext == '.md' and target_ext == '.html':
                with open(source_path, 'r', encoding='utf-8') as f:
                    md_content = f.read()
                html_content = markdown.markdown(md_content)
                with open(target_path, 'w', encoding='utf-8') as f:
                    f.write(f"<!DOCTYPE html><html><head><meta charset='utf-8'></head><body>{html_content}</body></html>")
                    
            elif source_ext == '.csv' and target_ext == '.xlsx':
                df = pd.read_csv(source_path)
                df.to_excel(target_path, index=False)
                
            elif source_ext == '.xlsx' and target_ext == '.csv':
                df = pd.read_excel(source_path)
                df.to_csv(target_path, index=False, encoding='utf-8')
                
            else:
                # 简单的文本复制
                with open(source_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(target_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            return True
        except Exception as e:
            print(f"文档转换错误: {e}")
            return False
    
    def convert_data(self, source_path, target_path, source_ext, target_ext):
        """转换数据格式"""
        try:
            if source_ext == '.csv' and target_ext == '.json':
                df = pd.read_csv(source_path)
                df.to_json(target_path, orient='records', ensure_ascii=False, indent=2)
                
            elif source_ext == '.json' and target_ext == '.csv':
                with open(source_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                df = pd.DataFrame(data)
                df.to_csv(target_path, index=False, encoding='utf-8')
                
            elif source_ext == '.xlsx' and target_ext == '.json':
                df = pd.read_excel(source_path)
                df.to_json(target_path, orient='records', ensure_ascii=False, indent=2)
                
            elif source_ext == '.json' and target_ext == '.xlsx':
                with open(source_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                df = pd.DataFrame(data)
                df.to_excel(target_path, index=False)
                
            return True
        except Exception as e:
            print(f"数据转换错误: {e}")
            return False
    
    def convert_file(self, source_path, target_path, source_ext, target_ext):
        """转换单个文件"""
        category = self.get_file_category(source_ext)
        
        if category == 'image':
            return self.convert_image(source_path, target_path, target_ext)
        elif category == 'document':
            return self.convert_document(source_path, target_path, source_ext, target_ext)
        elif category == 'data':
            return self.convert_data(source_path, target_path, source_ext, target_ext)
        
        return False


class FileConverterGUI:
    """文件转换器GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("文件格式转换器")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.converter = FileConverter()
        self.source_folder = tk.StringVar()
        self.target_folder = tk.StringVar()
        self.source_format = tk.StringVar()
        self.target_format = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="文件格式转换器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 源文件夹选择
        ttk.Label(main_frame, text="源文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.source_folder, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.select_source_folder).grid(row=1, column=2, pady=5)
        
        # 目标文件夹选择
        ttk.Label(main_frame, text="目标文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.target_folder, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.select_target_folder).grid(row=2, column=2, pady=5)
        
        # 格式选择框架
        format_frame = ttk.LabelFrame(main_frame, text="格式转换设置", padding="10")
        format_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        format_frame.columnconfigure(1, weight=1)
        format_frame.columnconfigure(3, weight=1)
        
        # 源格式选择
        ttk.Label(format_frame, text="源格式:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.source_format_combo = ttk.Combobox(format_frame, textvariable=self.source_format, state="readonly")
        self.source_format_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 10))
        self.source_format_combo.bind('<<ComboboxSelected>>', self.on_source_format_change)
        
        # 目标格式选择
        ttk.Label(format_frame, text="目标格式:").grid(row=0, column=2, sticky=tk.W, pady=5)
        self.target_format_combo = ttk.Combobox(format_frame, textvariable=self.target_format, state="readonly")
        self.target_format_combo.grid(row=0, column=3, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # 初始化格式选项
        self.update_format_options()
        
        # 转换按钮
        convert_button = ttk.Button(main_frame, text="开始转换", command=self.start_conversion)
        convert_button.grid(row=4, column=0, columnspan=3, pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪")
        self.status_label.grid(row=6, column=0, columnspan=3, pady=5)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="5")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

    def update_format_options(self):
        """更新格式选项"""
        all_extensions = set()
        for category_info in self.converter.supported_conversions.values():
            all_extensions.update(category_info['extensions'])

        self.source_format_combo['values'] = sorted(list(all_extensions))
        if all_extensions:
            self.source_format.set(sorted(list(all_extensions))[0])
            self.on_source_format_change()

    def on_source_format_change(self, event=None):
        """源格式改变时更新目标格式选项"""
        source_ext = self.source_format.get()
        target_formats = self.converter.get_target_formats(source_ext)

        # 移除源格式本身
        if source_ext in target_formats:
            target_formats = [fmt for fmt in target_formats if fmt != source_ext]

        self.target_format_combo['values'] = target_formats
        if target_formats:
            self.target_format.set(target_formats[0])

    def select_source_folder(self):
        """选择源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹")
        if folder:
            self.source_folder.set(folder)

    def select_target_folder(self):
        """选择目标文件夹"""
        folder = filedialog.askdirectory(title="选择目标文件夹")
        if folder:
            self.target_folder.set(folder)

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, status):
        """更新状态标签"""
        self.status_label.config(text=status)
        self.root.update_idletasks()

    def start_conversion(self):
        """开始转换过程"""
        # 验证输入
        if not self.source_folder.get():
            messagebox.showerror("错误", "请选择源文件夹")
            return

        if not self.target_folder.get():
            messagebox.showerror("错误", "请选择目标文件夹")
            return

        if not self.source_format.get() or not self.target_format.get():
            messagebox.showerror("错误", "请选择源格式和目标格式")
            return

        # 在新线程中执行转换
        threading.Thread(target=self.perform_conversion, daemon=True).start()

    def perform_conversion(self):
        """执行文件转换"""
        source_dir = Path(self.source_folder.get())
        target_dir = Path(self.target_folder.get())
        source_ext = self.source_format.get()
        target_ext = self.target_format.get()

        # 创建目标文件夹
        target_dir.mkdir(parents=True, exist_ok=True)

        # 查找源文件
        source_files = list(source_dir.glob(f"*{source_ext}"))

        if not source_files:
            self.log_message(f"在 {source_dir} 中未找到 {source_ext} 格式的文件")
            self.update_status("转换完成")
            return

        self.log_message(f"找到 {len(source_files)} 个 {source_ext} 文件")
        self.progress['maximum'] = len(source_files)
        self.progress['value'] = 0

        success_count = 0
        error_count = 0

        for i, source_file in enumerate(source_files):
            try:
                # 生成目标文件路径
                target_file = target_dir / f"{source_file.stem}{target_ext}"

                self.update_status(f"正在转换: {source_file.name}")
                self.log_message(f"转换: {source_file.name} -> {target_file.name}")

                # 执行转换
                if self.converter.convert_file(str(source_file), str(target_file), source_ext, target_ext):
                    success_count += 1
                    self.log_message(f"✓ 成功转换: {source_file.name}")
                else:
                    error_count += 1
                    self.log_message(f"✗ 转换失败: {source_file.name}")

            except Exception as e:
                error_count += 1
                self.log_message(f"✗ 转换错误 {source_file.name}: {str(e)}")

            # 更新进度条
            self.progress['value'] = i + 1
            self.root.update_idletasks()

        # 转换完成
        self.log_message(f"\n转换完成! 成功: {success_count}, 失败: {error_count}")
        self.update_status(f"转换完成 - 成功: {success_count}, 失败: {error_count}")

        if success_count > 0:
            messagebox.showinfo("转换完成", f"成功转换 {success_count} 个文件\n目标文件夹: {target_dir}")


def main():
    """主函数"""
    root = tk.Tk()
    app = FileConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
