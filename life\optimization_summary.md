# 番茄小说章节标题优化总结

## 优化目标
1. 在 `processFanqieChapters` 函数中将 `chapterIds` 和 `chapterTitles` 两个数组拼接成一个对象数组
2. 重构 `processBatchResponseArray` 函数中的小说标题添加方式，使其从新的对象数组中获取标题
3. 确保最终的小说按照 `chapterIds` 数组顺序组装章节

## 主要改动

### 1. 创建章节信息对象数组
在 `processFanqieChapters` 函数中，将原来分离的 `chapterIds` 和 `chapterTitles` 数组合并为一个包含完整章节信息的对象数组：

```java
// 将章节ID和标题拼接成对象数组
JSONArray chapterInfoArray = new JSONArray();
int totalChapters = chapterIds.size();

for (int i = 0; i < totalChapters; i++) {
    JSONObject chapterInfo = new JSONObject();
    chapterInfo.put("id", chapterIds.getStr(i));
    
    // 获取章节标题，如果标题数组存在且索引有效则使用，否则使用默认标题
    String chapterTitle = (chapterTitles != null && i < chapterTitles.size())
        ? chapterTitles.getStr(i)
        : "第" + (i + 1) + "章";
    chapterInfo.put("title", chapterTitle);
    chapterInfo.put("index", i); // 添加索引，确保按顺序组装
    
    chapterInfoArray.add(chapterInfo);
}
```

### 2. 更新函数签名
更新了以下函数的签名，使其接受章节信息数组而不是分离的ID和标题数组：

- `processBatchChapters`: 接受 `JSONArray chapterInfoArray` 参数
- `processBatchResponseArray`: 接受 `JSONArray chapterInfoArray` 参数
- `processBatchResponseObject`: 接受 `JSONArray chapterInfoArray` 参数
- `retryFailedChaptersIndividually`: 接受 `JSONArray chapterInfoArray` 参数

### 3. 重构章节标题获取逻辑
在 `processBatchResponseArray` 函数中，重构了章节标题的获取方式：

#### 新格式处理：
```java
// 从章节信息数组中获取章节标题 (优先使用响应中的标题)
String chapterTitle = title;
if (chapterTitle == null || chapterTitle.isEmpty()) {
    // 如果响应中的标题为空，从章节信息数组中获取
    if (chapterIndex < chapterInfoArray.size()) {
        JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
        chapterTitle = chapterInfo.getStr("title");
    } else {
        chapterTitle = "第" + (chapterIndex + 1) + "章";
    }
}
```

#### 原格式处理：
```java
// 从章节信息数组中获取章节标题
String chapterTitle;
if (chapterIndex < chapterInfoArray.size()) {
    JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
    chapterTitle = chapterInfo.getStr("title");
} else {
    chapterTitle = chapterData.getStr("title", "第" + (chapterIndex + 1) + "章");
}
```

### 4. 确保按顺序组装章节
在最终组装小说内容时，确保按照 `chapterIds` 数组的顺序：

```java
// 按chapterIds数组顺序添加章节内容
for (int i = 0; i < totalChapters; i++) {
    String chapterContent = chapterContents.getOrDefault(i, null);

    if (chapterContent != null) {
        novelContent.append(chapterContent);
    } else {
        // 对于仍然缺失的章节，从章节信息数组中获取标题并添加占位符
        JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
        String chapterTitle = chapterInfo.getStr("title");
        novelContent.append(chapterTitle).append("\r\n\r\n");
        novelContent.append("[章节内容获取失败，请稍后重试]\r\n\r\n");
        failedChapters.incrementAndGet();
    }
}
```

### 5. 更新重试机制
在 `retryFailedChaptersIndividually` 函数中，也使用章节信息数组来获取章节ID和标题：

```java
JSONObject chapterInfo = chapterInfoArray.getJSONObject(index);
String chapterId = chapterInfo.getStr("id");
String chapterTitle = chapterInfo.getStr("title");
```

## 优化效果

1. **数据结构统一**: 将分离的章节ID和标题数组合并为统一的对象数组，提高了数据的一致性和可维护性
2. **标题获取优化**: 标题获取逻辑更加清晰，优先使用响应中的标题，其次使用预设的标题
3. **顺序保证**: 确保最终生成的小说文件严格按照 `chapterIds` 数组的顺序组装章节
4. **代码简化**: 减少了函数参数的数量，简化了函数调用
5. **错误处理改进**: 在章节获取失败时，能够正确显示预设的章节标题

## 兼容性
- 保持了对原有 `chapterIds` 和 `chapterTitles` 数组的兼容性
- 如果 `chapterTitles` 为空或不存在，会自动生成默认标题
- 所有现有的API调用方式保持不变
