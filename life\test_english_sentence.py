#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试英语金句插件的核心功能
"""

import sys
import os
import asyncio
import tempfile
from unittest.mock import Mock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟AstrBot的依赖
class MockContext:
    async def send_message(self, origin, message):
        print(f"发送消息到 {origin}: {message}")

class MockEvent:
    def __init__(self, origin="test_origin"):
        self.unified_msg_origin = origin
    
    def plain_result(self, text):
        return f"回复: {text}"

# 尝试导入重构后的插件
try:
    # 模拟必要的模块
    sys.modules['astrbot.api.event'] = Mock()
    sys.modules['astrbot.api.star'] = Mock()
    sys.modules['astrbot.api.message_components'] = Mock()
    sys.modules['astrbot.api.event.filter'] = Mock()
    sys.modules['astrbot.api'] = Mock()
    
    # 创建模拟的类和函数
    def mock_register(name, cmd, desc, version):
        def decorator(cls):
            return cls
        return decorator
    
    class MockStar:
        def __init__(self, context):
            self.context = context
    
    class MockMessageChain:
        def __init__(self, components):
            self.components = components
        
        def __str__(self):
            return str(self.components)
    
    class MockPlain:
        def __init__(self, text):
            self.text = text
        
        def __str__(self):
            return self.text
    
    class MockImage:
        def __init__(self, path=None, url=None):
            self.path = path
            self.url = url
        
        def __str__(self):
            return f"Image(path={self.path}, url={self.url})"
    
    # 设置模拟对象
    sys.modules['astrbot.api.star'].register = mock_register
    sys.modules['astrbot.api.star'].Star = MockStar
    sys.modules['astrbot.api.star'].Context = MockContext
    sys.modules['astrbot.api.event'].MessageChain = MockMessageChain
    sys.modules['astrbot.api.message_components'].Plain = MockPlain
    sys.modules['astrbot.api.message_components'].Image = MockImage
    sys.modules['astrbot.api'].logger = Mock()
    
    # 现在导入插件
    from zaobao import EnglishSentencePlugin
    
    print("✅ 插件导入成功！")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

async def test_plugin():
    """测试插件的核心功能"""
    print("\n🧪 开始测试英语金句插件...")
    
    # 创建插件实例
    context = MockContext()
    plugin = EnglishSentencePlugin(context)
    
    print("✅ 插件实例创建成功")
    
    # 测试备用句子功能
    print("\n📝 测试备用句子功能...")
    english, chinese = plugin.get_backup_sentence()
    print(f"英文: {english[:50]}...")
    print(f"翻译: {chinese[:50]}...")
    
    # 测试图片生成功能
    print("\n🎨 测试图片生成功能...")
    try:
        image_path = await plugin.generate_sentence_image_async(english, chinese)
        if image_path and os.path.exists(image_path):
            print(f"✅ 图片生成成功: {image_path}")
            # 清理测试文件
            os.remove(image_path)
            print("🧹 测试文件已清理")
        else:
            print("⚠️ 图片生成失败或文件不存在")
    except Exception as e:
        print(f"❌ 图片生成测试失败: {e}")
    
    # 测试API调用功能（可能会失败，这是正常的）
    print("\n📡 测试API调用功能...")
    try:
        api_result = await plugin.get_sentence_from_api()
        if api_result:
            english_api, chinese_api = api_result
            print(f"✅ API调用成功")
            print(f"英文: {english_api[:50]}...")
            print(f"翻译: {chinese_api[:50]}...")
        else:
            print("⚠️ API调用失败（这是正常的，可能是网络或API密钥问题）")
    except Exception as e:
        print(f"⚠️ API调用异常: {e}")
    
    # 测试命令处理
    print("\n⌨️ 测试命令处理...")
    event = MockEvent()
    
    try:
        # 测试帮助命令
        help_result = await plugin.show_help(event)
        print(f"✅ 帮助命令测试成功")
        
        # 测试状态命令
        status_result = await plugin.sentence_status(event)
        print(f"✅ 状态命令测试成功")
        
    except Exception as e:
        print(f"❌ 命令处理测试失败: {e}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    # 运行测试
    try:
        asyncio.run(test_plugin())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
