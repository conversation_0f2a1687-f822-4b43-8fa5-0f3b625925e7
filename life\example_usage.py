#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英语句子图片生成器 - 使用示例
优化版本展示不同主题和功能
"""

from english_sentence import generate_sentence_image
import random

def demo_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    sentence_data = [
        "英文：Success is not final, failure is not fatal: it is the courage to continue that counts.",
        "翻译：成功不是终点，失败不是致命的：重要的是继续前进的勇气。"
    ]
    
    # 使用默认设置生成图片
    output_path = generate_sentence_image(sentence_data, "demo_basic.png")
    print(f"基础示例图片已生成：{output_path}")

def demo_color_themes():
    """不同颜色主题示例"""
    print("\n=== 不同颜色主题示例 ===")
    
    sentence_data = [
        "英文：The only way to do great work is to love what you do.",
        "翻译：做出伟大工作的唯一方法就是热爱你所做的事情。"
    ]
    
    # 定义主题色彩
    themes = {
        "优雅红色": (220, 85, 85),
        "清新绿色": (67, 160, 71),
        "专业蓝色": (33, 150, 243),
        "神秘紫色": (156, 39, 176),
        "温暖橙色": (255, 138, 101),
    }
    
    for theme_name, color in themes.items():
        filename = f"demo_{theme_name.lower().replace(' ', '_')}.png"
        output_path = generate_sentence_image(
            sentence_data, 
            filename, 
            bg_color=color
        )
        print(f"{theme_name}主题图片已生成：{output_path}")

def demo_long_sentence():
    """长句子处理示例"""
    print("\n=== 长句子处理示例 ===")
    
    sentence_data = [
        "英文：In the rapidly evolving landscape of artificial intelligence and machine learning, we are witnessing unprecedented changes that are reshaping industries, transforming educational paradigms, and revolutionizing the way we interact with technology in our daily lives.",
        "翻译：在人工智能和机器学习快速发展的环境中，我们正在见证前所未有的变化，这些变化正在重塑各个行业，改变教育模式，并彻底改变我们在日常生活中与技术互动的方式。"
    ]
    
    # 生成长句子图片，启用强制标点换行
    output_path = generate_sentence_image(
        sentence_data, 
        "demo_long_sentence.png",
        force_punctuation_wrap=True
    )
    print(f"长句子示例图片已生成：{output_path}")

def demo_custom_fonts():
    """自定义字体大小示例"""
    print("\n=== 自定义字体大小示例 ===")
    
    sentence_data = [
        "英文：Education is the most powerful weapon which you can use to change the world.",
        "翻译：教育是你可以用来改变世界的最强大的武器。"
    ]
    
    # 使用更大的字体
    output_path = generate_sentence_image(
        sentence_data, 
        "demo_large_fonts.png",
        font_size_en=48,  # 更大的英文字体
        font_size_cn=36   # 更大的中文字体
    )
    print(f"大字体示例图片已生成：{output_path}")

def demo_random_theme():
    """随机主题示例"""
    print("\n=== 随机主题示例 ===")
    
    sentences = [
        [
            "英文：The future belongs to those who believe in the beauty of their dreams.",
            "翻译：未来属于那些相信自己梦想之美的人。"
        ],
        [
            "英文：It does not matter how slowly you go as long as you do not stop.",
            "翻译：只要你不停下脚步，走得多慢都没关系。"
        ],
        [
            "英文：Life is what happens to you while you're busy making other plans.",
            "翻译：生活就是当你忙于制定其他计划时发生在你身上的事情。"
        ]
    ]
    
    colors = [
        (220, 85, 85),   # 红色
        (67, 160, 71),   # 绿色
        (33, 150, 243),  # 蓝色
        (156, 39, 176),  # 紫色
        (255, 138, 101), # 橙色
    ]
    
    for i, sentence_data in enumerate(sentences):
        random_color = random.choice(colors)
        filename = f"demo_random_{i+1}.png"
        output_path = generate_sentence_image(
            sentence_data, 
            filename,
            bg_color=random_color
        )
        print(f"随机主题示例 {i+1} 已生成：{output_path}")

def main():
    """主函数 - 运行所有示例"""
    print("🎨 英语句子图片生成器 - 优化版本演示")
    print("=" * 50)
    
    try:
        # 运行各种示例
        demo_basic_usage()
        demo_color_themes()
        demo_long_sentence()
        demo_custom_fonts()
        demo_random_theme()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例图片生成完成！")
        print("\n📁 生成的文件列表：")
        
        # 列出生成的文件
        import os
        png_files = [f for f in os.listdir('.') if f.startswith('demo_') and f.endswith('.png')]
        for i, filename in enumerate(sorted(png_files), 1):
            print(f"   {i:2d}. {filename}")
            
        print(f"\n🎉 总共生成了 {len(png_files)} 张示例图片！")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
