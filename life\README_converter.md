# 文件格式转换器

一个用Python开发的可视化文件格式转换工具，支持多种常见文件格式之间的转换。

## 功能特性

- **图像格式转换**: 支持 JPG, PNG, BMP, GIF, TIFF, WebP 等格式互转
- **文档格式转换**: 支持 TXT, MD, DOCX, CSV, HTML 等格式互转
- **数据格式转换**: 支持 CSV, JSON, XLSX 等格式互转
- **批量转换**: 一次性转换指定文件夹下的所有同类型文件
- **可视化界面**: 直观易用的图形用户界面
- **实时进度**: 显示转换进度和详细日志

## 支持的转换类型

### 图像格式
- 输入格式: `.jpg`, `.jpeg`, `.png`, `.bmp`, `.gif`, `.tiff`, `.webp`
- 输出格式: `.jpg`, `.png`, `.bmp`, `.gif`, `.tiff`, `.webp`

### 文档格式
- 输入格式: `.txt`, `.md`, `.docx`, `.csv`
- 输出格式: `.txt`, `.md`, `.docx`, `.csv`, `.html`

### 数据格式
- 输入格式: `.csv`, `.json`, `.xlsx`
- 输出格式: `.csv`, `.json`, `.xlsx`

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **启动程序**:
   ```bash
   python file_converter_gui.py
   ```

2. **选择文件夹**:
   - 点击"浏览"按钮选择包含源文件的文件夹
   - 选择转换后文件的保存文件夹

3. **选择格式**:
   - 从下拉菜单中选择源文件格式
   - 选择目标转换格式

4. **开始转换**:
   - 点击"开始转换"按钮
   - 查看进度条和日志信息
   - 转换完成后会显示结果统计

## 界面说明

- **源文件夹**: 包含待转换文件的文件夹路径
- **目标文件夹**: 转换后文件的保存位置
- **源格式**: 要转换的文件格式
- **目标格式**: 转换后的文件格式
- **进度条**: 显示当前转换进度
- **转换日志**: 显示详细的转换过程和结果

## 注意事项

1. **文件覆盖**: 如果目标文件夹中存在同名文件，将会被覆盖
2. **格式兼容**: 某些格式转换可能会丢失部分信息（如RGBA到RGB）
3. **文件编码**: 文本文件默认使用UTF-8编码
4. **大文件处理**: 处理大量文件时请耐心等待

## 错误处理

程序会自动处理常见错误：
- 文件读取失败
- 格式不支持
- 权限问题
- 磁盘空间不足

所有错误信息都会显示在转换日志中。

## 扩展功能

如需添加新的文件格式支持，可以修改 `FileConverter` 类中的 `supported_conversions` 字典和相应的转换方法。

## 系统要求

- Python 3.7+
- Windows/macOS/Linux
- 足够的磁盘空间用于存储转换后的文件
