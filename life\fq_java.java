package com.ruoyi.proxy.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.novel.domain.NovelFanqie;
import com.ruoyi.novel.service.INovelFanqieService;
import com.ruoyi.novel.utils.FileUploadUtils;
import com.ruoyi.proxy.domain.FqToken;
import com.ruoyi.proxy.domain.FqVersion;
import com.ruoyi.proxy.service.FqVersionService;
import com.ruoyi.proxy.service.IFqTokenService;
import com.ruoyi.proxy.utils.IpUtils;
import com.ruoyi.toolManager.entity.ToolIntegral;
import com.ruoyi.toolManager.mapper.ToolIntegralMapper;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.TimeoutException;


/**
 * @Classname TokenController
 * @Date 2024/6/3 17:47
 * @Created by PG_Code
 */
@Slf4j
@RestController
@RequestMapping("/fq_token")
public class FqTokenController extends BaseController {

    @Autowired
    private IFqTokenService tokenService;

    @Autowired
    private FqVersionService versionService;

    @Autowired
    private INovelFanqieService novelFanqieService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 工具积分
     */
    @Autowired
    private ToolIntegralMapper toolIntegralMapper;

    @Value("${fq_token.url}")
    private String FANQIE_URL ;

    private final AtomicInteger taskCounter = new AtomicInteger(0);

    private static final String TASK_STATUS_PENDING = "PENDING";
    private static final String TASK_STATUS_PROCESSING = "PROCESSING";
    private static final String TASK_STATUS_COMPLETED = "COMPLETED";
    private static final String TASK_STATUS_FAILED = "FAILED";

    // 修改任务过期时间为1小时
    private static final String REDIS_TASK_KEY_PREFIX = "novel_download_task:";
    private static final int TASK_EXPIRE_HOURS = 1;

    /**
     * 查询所有Token
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:selectAll')")
    @GetMapping("/selectAll")
    public AjaxResult selectAll() {
        return success(tokenService.selectAll());
    }


    /**
     * 根据名称查询
     *
     * @param tokenName
     * @return
     */
    @GetMapping("/selectByName")
    public AjaxResult selectByName(@RequestParam String tokenName) {

        //获取工具信息
        String toolName = "番茄工具";
        ToolIntegral tool = toolIntegralMapper.selectToolIntegralByToolName(toolName);
        //判断是否禁止
        if(tool.getToolStatus() == 1){
            return warn("工具维护中...");
        }

        return tokenService.selectByName(tokenName);
    }

    /**
     * 根据id查询
     *
     * @param tokenId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:selectById')")
    @GetMapping("/selectById")
    public AjaxResult selectById(@RequestParam Integer tokenId) {
        return AjaxResult.success(tokenService.selectById(tokenId));
    }

    @PreAuthorize("@ss.hasPermi('fq_token:handle:selectByPage')")
    @GetMapping("/selectByPage")
    public TableDataInfo selectByPage(FqToken token) {
        startPage();
        List<FqToken> list = tokenService.selectByPage(token);
        return getDataTable(list);
    }

    /**根据token重置激活码
     *
     * @param token
     * @return
     */
    @PostMapping("/resetMachineCodeByTokenCode")
    public AjaxResult resetCodeByToken(@RequestBody FqToken token){
        return tokenService.resetCodeByToken(token);
    }

    /**
     * 重置所有没有被删除和禁用的激活码的机器码
     * @return
     */
    @PostMapping("/reset_all_machine_code")
    public AjaxResult resetAllMachineCode(){
        return tokenService.resetAllCode();
    }

    /**
     * 新增Token
     *
     * @param token
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:insert')")
    @PutMapping("/insert")
    public AjaxResult insert(@RequestBody JSONObject token) throws ParseException {

        //检查是否重复获取token
        FqToken fqToken1 = tokenService.selectFqTokenByUserId(getUserId());
        if(fqToken1 != null){
            throw new ServiceException("您已经获取过token了，请勿重复获取");
        }

        FqToken t = new FqToken();
        t.setTokenName(token.getStr("tokenName"));
        if(token.getDate("tokenTime")==null){
            //获取当前时间，时分秒
            Date now = new Date();
            SimpleDateFormat  dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = dateFormat.format(now);
            Date parsedDate = dateFormat.parse(format);
            t.setTokenTime(parsedDate);
        }else {
            t.setTokenTime(token.getDate("tokenTime"));
        }
        t.setUserId(getUserId());
        t.setCreateBy(getUsername());
        return tokenService.insert(t) > 0 ? AjaxResult.success() : AjaxResult.error();
    }


    /**
     * 删除Token
     *
     * @param tokenId
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:delete')")
    @DeleteMapping("/delete")
    public AjaxResult delete(@RequestParam Integer tokenId) {
        return tokenService.delete(tokenId) > 0 ? AjaxResult.success() : AjaxResult.error();
    }


    /**
     * 修改
     *
     * @param tokenName
     * @return
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestParam String tokenName, @RequestParam String tokenCompCode) {
        return tokenService.update(tokenName, tokenCompCode) > 0 ? AjaxResult.success() : AjaxResult.error();
    }



    /**
     * 修改使用次数
     *
     * @param novelData 小说数据
     * @return 任务ID
     */
    @PostMapping("/update_num_last")
    public AjaxResult updateNum(
            @RequestBody JSONObject novelData,
            HttpServletRequest request) {

        //获取工具信息
        String toolName = "番茄工具";
        ToolIntegral tool = toolIntegralMapper.selectToolIntegralByToolName(toolName);
        //判断是否禁止
        if(tool.getToolStatus() == 1){
            return warn("工具维护中...");
        }

        String tokenName = novelData.getStr("tokenName");
        String novelId = novelData.getStr("novelId");
        String novelAuthor = novelData.getStr("novelAuthor");  //作者
        String novelName = novelData.getStr("novelName");  //小说名称
        String novelDesc = novelData.getStr("novelDesc"); //小说描述
        String clientVersion = novelData.getStr("clientVersion");
        JSONArray novelChapterIds = new JSONArray();
        JSONArray novelChapterTitles = new JSONArray();
        //处理番茄
        if(novelId.length() > 9){
            novelChapterIds = novelData.getJSONArray("novelChapterIds");
            // 读取章节标题数组，如果不存在则设为null
            if (novelData.containsKey("novelChapterTitles")) {
                novelChapterTitles = novelData.getJSONArray("novelChapterTitles");
            }
        }

        // 获取客户端IP地址
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========【{}】开始下载小说【{}】，客户端版本：{}，IP地址：{}========>",
                tokenName, novelId, clientVersion, ipAddr);

        // IP访问频率检查
        String ipCheckResult = checkIpFrequency(ipAddr);
        if (!"200".equals(ipCheckResult)) {
            log.warn("<==========【{}】下载小说【{}】失败：IP访问频率异常 {}========>", tokenName, novelId, ipAddr);
            return Objects.requireNonNull(AjaxResult.error().put("code", ipCheckResult)).put("msg", "IP访问频率异常");
        }

        //第一版本
        if(clientVersion==null){
            throw new ServiceException("请在群文件下载最新版");
        }

        // 验证客户端版本
        if (!clientVersion.isEmpty()) {
            FqVersion latestVersion = versionService.checkUpdate(clientVersion, "All", "All");
            if (latestVersion != null && latestVersion.getIsMandatory() == 1) {
                // 需要强制更新，不允许使用
                log.warn("<==========【{}】下载小说【{}】失败：客户端版本过低，需要强制更新========>", tokenName, novelId);
                return Objects.requireNonNull(AjaxResult.error().put("code", "510")).put("msg", "客户端版本过低，需要强制更新");
            }
        }

        int i = tokenService.updateNum(tokenName, novelId);

        if (i == 520) {
            log.warn("<==========【{}】下载小说【{}】失败：激活码已禁用========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "520")).put("msg", "激活码已禁用");
        } else if (i == 510) {
            log.warn("<==========【{}】下载小说【{}】失败：激活码未激活========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "510")).put("msg", "激活码未激活");
        } else if (i == 530) {
            log.warn("<==========【{}】下载小说【{}】失败：超过使用额度限制========>", tokenName, novelId);
            return Objects.requireNonNull(AjaxResult.error().put("code", "530")).put("msg", "超过今日使用额度限制");
        } else if (i == 200) {
            String taskId = "";
            // 重复下载，创建任务ID并开始异步下载
            if(novelId.length() > 9){
                taskId = createDownloadTask(tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds, novelChapterTitles);
            }
            log.info("<==========【{}】重复下载小说【{}】，不计入次数，任务ID：{}========>", tokenName, novelId, taskId);
            return AjaxResult.success().put("taskId", taskId);
        }

        String taskId = "";
        if(novelId.length() > 9){
            taskId = createDownloadTask(tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds, novelChapterTitles);
        }

        log.info("<==========【{}】下载小说【{}】成功，任务ID：{}========>", tokenName, novelId, taskId);
        return AjaxResult.success().put("taskId", taskId);
    }

    /**
     * 创建下载任务并开始异步处理
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param novelChapterIds 章节ID数组
     * @param novelChapterTitles 章节标题数组
     * @return 任务ID
     */
    private String createDownloadTask(String tokenName, String novelId, String novelName, String novelAuthor,
                                      String novelDesc, JSONArray novelChapterIds, JSONArray novelChapterTitles) {
        // 检查是否已经有相同小说的下载任务正在进行中
        String novelTaskKey = "novel_downloading:" + novelId;
        Boolean isDownloading = redisTemplate.hasKey(novelTaskKey);
        String existingTaskId = null;

        if (isDownloading != null && isDownloading) {
            // 获取正在进行的任务ID
            existingTaskId = (String) redisTemplate.opsForValue().get(novelTaskKey);
            if (existingTaskId != null) {
                // 检查任务是否仍然存在
                String redisTaskKey = REDIS_TASK_KEY_PREFIX + existingTaskId;
                Boolean taskExists = redisTemplate.hasKey(redisTaskKey);

                if (taskExists != null && taskExists) {
                    // 获取任务状态
                    String status = (String) redisTemplate.opsForHash().get(redisTaskKey, "status");

                    // 获取任务创建时间
                    Object createTimeObj = redisTemplate.opsForHash().get(redisTaskKey, "createTime");
                    Date createTime = null;
                    if (createTimeObj instanceof Date) {
                        createTime = (Date) createTimeObj;
                    }

                    // 检查任务是否处于处理中或等待中状态，且未超过30分钟
                    boolean isValidTask = (TASK_STATUS_PROCESSING.equals(status) || TASK_STATUS_PENDING.equals(status));
                    boolean isNotStuck = true;

                    // 如果任务创建时间超过30分钟，认为任务可能卡住
                    if (createTime != null) {
                        long taskAgeMinutes = (System.currentTimeMillis() - createTime.getTime()) / (60 * 1000);
                        isNotStuck = taskAgeMinutes < 30;
                    }

                    if (isValidTask && isNotStuck) {
                        log.info("<==========【{}】小说【{}】已有下载任务正在进行中，任务ID：{}========>",
                                tokenName, novelId, existingTaskId);

                        // 更新任务信息，添加当前用户到共享列表
                        redisTemplate.opsForHash().put(redisTaskKey, "sharedUsers",
                                tokenName + "," + redisTemplate.opsForHash().get(redisTaskKey, "sharedUsers"));

                        return existingTaskId;
                    } else if (!isNotStuck) {
                        // 任务可能卡住，记录日志
                        log.warn("<==========【{}】小说【{}】已有下载任务可能卡住，状态：{}，将创建新任务========>",
                                tokenName, novelId, status);

                        // 标记原任务为失败
                        redisTemplate.opsForHash().put(redisTaskKey, "status", TASK_STATUS_FAILED);
                        redisTemplate.opsForHash().put(redisTaskKey, "errorMsg", "任务执行时间过长，可能卡住");

                        // 删除小说下载锁，允许创建新任务
                        redisTemplate.delete(novelTaskKey);
                    }
                }
            }
        }

        // 检查当前系统下载任务数量，避免系统过载
        Long currentTaskCount = redisTemplate.keys(REDIS_TASK_KEY_PREFIX + "*").stream()
                .filter(key -> {
                    String status = (String) redisTemplate.opsForHash().get(key, "status");

                    // 只统计处理中和等待中的任务
                    if (TASK_STATUS_PROCESSING.equals(status) || TASK_STATUS_PENDING.equals(status)) {
                        // 检查任务是否卡住（超过30分钟）
                        Object createTimeObj = redisTemplate.opsForHash().get(key, "createTime");
                        if (createTimeObj instanceof Date) {
                            Date createTime = (Date) createTimeObj;
                            long taskAgeMinutes = (System.currentTimeMillis() - createTime.getTime()) / (60 * 1000);

                            // 任务时间超过30分钟，认为可能卡住，不计入活跃任务
                            if (taskAgeMinutes >= 30) {
                                // 标记任务为失败
                                redisTemplate.opsForHash().put(key, "status", TASK_STATUS_FAILED);
                                redisTemplate.opsForHash().put(key, "errorMsg", "任务执行时间过长，可能卡住");
                                return false;
                            }
                        }
                        return true;
                    }
                    return false;
                })
                .count();

        // 设置最大并发下载任务数
        final int MAX_CONCURRENT_TASKS = 10; // 从20减少到10，降低系统负载

        // 生成唯一任务ID
        String taskId = generateTaskId(tokenName, novelId);

        // 创建任务状态信息
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("taskId", taskId);
        taskInfo.put("tokenName", tokenName);
        taskInfo.put("novelId", novelId);
        taskInfo.put("novelName", novelName);
        taskInfo.put("status", TASK_STATUS_PENDING);
        taskInfo.put("progress", 0);
        taskInfo.put("createTime", new Date());
        taskInfo.put("updateTime", new Date());
        taskInfo.put("totalChapters", novelChapterIds.size());
        taskInfo.put("sharedUsers", tokenName); // 记录共享此任务的用户

        // 保存任务状态到Redis
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        redisTemplate.opsForHash().putAll(redisKey, taskInfo);
        redisTemplate.expire(redisKey, TASK_EXPIRE_HOURS, TimeUnit.HOURS);

        // 标记该小说正在下载中，避免重复下载
        redisTemplate.opsForValue().set(novelTaskKey, taskId, 1, TimeUnit.HOURS); // 设置1小时过期，防止任务异常导致锁无法释放

        //查询小说是否存在并且章节数大于等于当前请求的章节数
        NovelFanqie existingNovel = novelFanqieService.getNovelFanqieByNovelId(novelId);
        if (existingNovel != null) {
            try {
                int existingChapterCount = Integer.parseInt(existingNovel.getNovelChapterNum());
                int requestedChapterCount = novelChapterIds.size();

                // 如果数据库中的章节数大于等于请求的章节数，直接返回已有URL
                if (existingChapterCount >= requestedChapterCount && existingNovel.getNovelUrl() != null && !existingNovel.getNovelUrl().isEmpty()) {
                    log.info("<==========【{}】小说【{}】在数据库中已存在且章节数满足({}>={}), 直接返回已有URL========>",
                            tokenName, novelId, existingChapterCount, requestedChapterCount);
                    updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, existingNovel.getNovelUrl(), null);

                    // 释放小说下载锁
                    redisTemplate.delete(novelTaskKey);

                    return taskId;
                }

                // 如果数据库中的章节数小于请求的章节数，需要更新
                log.info("<==========【{}】小说【{}】在数据库中已存在但章节数不足({}<{}), 需要更新========>",
                        tokenName, novelId, existingChapterCount, requestedChapterCount);
            } catch (NumberFormatException e) {
                log.warn("<==========【{}】小说【{}】在数据库中的章节数格式无效: {}========>",
                        tokenName, novelId, existingNovel.getNovelChapterNum());
            }
        } else {
            log.info("<==========【{}】小说【{}】在数据库中不存在, 需要新建========>", tokenName, novelId);
        }

        // 如果当前任务数超过限制，使用排队机制但不等待
        if (currentTaskCount >= MAX_CONCURRENT_TASKS) {
            log.warn("<==========系统当前下载任务数({})已达上限，新任务将排队等待========>", currentTaskCount);
            // 更新任务状态为排队中
            updateTaskStatus(taskId, TASK_STATUS_PENDING, 0, null, "任务排队中，等待执行");
        }

        // 启动异步任务
        CompletableFuture.runAsync(() -> {
            try {
                // 如果当前任务数超过限制，需要等待
                if (currentTaskCount >= MAX_CONCURRENT_TASKS) {
                    // 任务排队中，每10秒检查一次是否可以开始执行
                    int waitCount = 0;
                    final int MAX_WAIT_TIMES = 30; // 最多等待5分钟(30次*10秒)

                    while (waitCount < MAX_WAIT_TIMES) {
                        // 检查当前活跃任务数
                        Long activeTaskCount = redisTemplate.keys(REDIS_TASK_KEY_PREFIX + "*").stream()
                                .filter(key -> {
                                    String status = (String) redisTemplate.opsForHash().get(key, "status");
                                    return TASK_STATUS_PROCESSING.equals(status);
                                })
                                .count();

                        // 如果活跃任务数低于阈值，开始执行
                        if (activeTaskCount < MAX_CONCURRENT_TASKS) {
                            log.info("<==========【{}】小说【{}】排队结束，开始执行，当前活跃任务数：{}========>",
                                    tokenName, novelId, activeTaskCount);
                            break;
                        }

                        // 更新等待消息
                        waitCount++;
                        updateTaskStatus(taskId, TASK_STATUS_PENDING, 0, null,
                                String.format("任务排队中，等待执行，已等待%d秒", waitCount * 10));

                        // 等待10秒后再次检查
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.warn("<==========【{}】小说【{}】等待中断========>", tokenName, novelId);
                            break;
                        }
                    }

                    // 如果等待超时，更新任务状态
                    if (waitCount >= MAX_WAIT_TIMES) {
                        log.warn("<==========【{}】小说【{}】等待超时，强制开始执行========>", tokenName, novelId);
                        updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 5, null, "等待超时，强制开始执行");
                    }
                }

                downloadNovelAsync(taskId, tokenName, novelId, novelName, novelAuthor, novelDesc, novelChapterIds, novelChapterTitles);
            } catch (Exception e) {
                log.error("<==========【{}】小说【{}】任务启动失败：{}========>",
                        tokenName, novelId, e.getMessage(), e);
                updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "任务启动失败：" + e.getMessage());

                // 释放小说下载锁
                redisTemplate.delete(novelTaskKey);
            }
        });

        return taskId;
    }

    /**
     * 生成唯一任务ID
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @return 任务ID
     */
    private String generateTaskId(String tokenName, String novelId) {
        // 生成唯一任务ID: 时间戳_计数器_用户标识_随机数
        return String.format("%d_%d_%s_%s_%d",
                System.currentTimeMillis(),
                taskCounter.incrementAndGet(),
                tokenName.substring(0, Math.min(6, tokenName.length())),
                novelId.substring(0, Math.min(6, novelId.length())),
                new Random().nextInt(1000));
    }

    /**
     * 异步下载小说
     * @param taskId 任务ID
     * @param tokenName 激活码
     * @param novelId 小说ID
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param novelChapterIds 章节ID数组
     * @param novelChapterTitles 章节标题数组
     */
    @Async
    public void downloadNovelAsync(String taskId, String tokenName, String novelId, String novelName,
                                   String novelAuthor, String novelDesc, JSONArray novelChapterIds, JSONArray novelChapterTitles) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;
        String novelTaskKey = "novel_downloading:" + novelId;

        try {
            // 更新任务状态为处理中
            updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 10, null, null);

            // 处理番茄小说内容
            if (novelId.length() > 9 && novelChapterIds != null && !novelChapterIds.isEmpty()) {
                // 更新任务进度
                updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 20, null, null);

                // 查询数据库中是否已有该小说的记录
                NovelFanqie existingNovel = novelFanqieService.getNovelFanqieByNovelId(novelId);
                boolean needFullDownload = true;
                String existingFileUrl = null;

                // 检查数据库中是否有该小说，且章节数是否小于当前请求的章节数
                if (existingNovel != null) {
                    int existingChapterCount = Integer.parseInt(existingNovel.getNovelChapterNum());
                    int requestedChapterCount = novelChapterIds.size();

                    log.info("<==========小说【{}】在数据库中已存在，数据库章节数：{}，请求章节数：{}========>",
                            novelId, existingChapterCount, requestedChapterCount);

                    if (existingChapterCount >= requestedChapterCount) {
                        // 数据库中的章节数已经满足或超过请求的章节数，直接返回已有的URL
                        log.info("<==========小说【{}】数据库中章节数已满足请求，直接返回已有URL========>", novelId);
                        updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, existingNovel.getNovelUrl(), null);

                        // 释放小说下载锁
                        redisTemplate.delete(novelTaskKey);

                        return;
                    } else {
                        // 数据库中有记录但章节数不足，需要更新
                        log.info("<==========小说【{}】数据库中章节数不足，需要更新，从{}章更新到{}章========>",
                                novelId, existingChapterCount, requestedChapterCount);
                        existingFileUrl = existingNovel.getNovelUrl();
                    }
                }

                // 下载小说内容
                String novelContent = null;
                try {
                    novelContent = processFanqieChapters(taskId, novelId, novelName, novelAuthor, novelDesc, novelChapterIds, novelChapterTitles);
                } catch (Exception e) {
                    log.error("<==========【{}】小说【{}】下载章节内容异常：{}========>",
                            tokenName, novelId, e.getMessage(), e);
                    updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "下载章节内容异常：" + e.getMessage());

                    // 释放小说下载锁
                    redisTemplate.delete(novelTaskKey);

                    return;
                }

                // 下载完成后，更新进度到60%
                updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 60, null, null);

                if (novelContent != null && !novelContent.isEmpty()) {
                    // 将内容转换为MultipartFile
                    Path tempFile = null;
                    try {
                        // 创建临时文件
                        tempFile = Files.createTempFile("novel_" + novelId + "_", ".txt");
                        Files.write(tempFile, novelContent.getBytes("UTF-8"));

                        // 转换为MultipartFile
                        MultipartFile novelFanqieFile = new MockMultipartFile(
                                "novel_" + novelId + ".txt",
                                "novel_" + novelId + ".txt",
                                "text/plain",
                                Files.readAllBytes(tempFile)
                        );

                        // 更新任务进度
                        updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 80, null, null);

                        // 上传到MinIO
                        String fileUrl = null;
                        try {
                            fileUrl = FileUploadUtils.uploadMinio(novelFanqieFile, novelId);
                        } catch (Exception e) {
                            log.error("<==========【{}】小说【{}】上传到MinIO失败：{}========>",
                                    tokenName, novelId, e.getMessage(), e);
                            updateTaskStatus(taskId, TASK_STATUS_FAILED, 60, null, "上传到MinIO失败：" + e.getMessage());

                            // 释放小说下载锁
                            redisTemplate.delete(novelTaskKey);

                            return;
                        }

                        log.info("<==========【{}】小说【{}】异步下载完成，文件URL：{}========>",
                                tokenName, novelId, fileUrl);

                        //插入或更新数据库
                        if(fileUrl != null && !fileUrl.isEmpty()){
                            try {
                                //首先查询数据库是否有
                                NovelFanqie novelFanqieByNovelId = novelFanqieService.getNovelFanqieByNovelId(novelId);
                                if(novelFanqieByNovelId!=null){
                                    novelFanqieByNovelId.setNovelChapterNum(String.valueOf(novelChapterIds.size()));
                                    novelFanqieByNovelId.setNovelUrl(fileUrl);
                                    novelFanqieByNovelId.setUpdateUserCode(tokenName);
                                    novelFanqieByNovelId.setUpdateTime(new Date()); // 添加更新时间
                                    novelFanqieService.updateNovelFanqie(novelFanqieByNovelId);
                                    log.info("<==========【{}】小说【{}】数据库记录已更新，章节数：{}========>",
                                            tokenName, novelId, novelChapterIds.size());
                                }else {
                                    NovelFanqie novelFanqie = new NovelFanqie();
                                    novelFanqie.setNovelId(novelId);
                                    novelFanqie.setNovelChapterNum(String.valueOf(novelChapterIds.size()));
                                    novelFanqie.setNovelName(novelName);
                                    novelFanqie.setNovelAuthor(novelAuthor);
                                    novelFanqie.setNovelDesc(novelDesc);
                                    novelFanqie.setNovelUrl(fileUrl);
                                    novelFanqie.setCreateTime(new Date()); // 添加创建时间
                                    novelFanqie.setCreateUserCode(tokenName);
                                    novelFanqieService.insertNovelFanqie(novelFanqie);
                                    log.info("<==========【{}】小说【{}】数据库记录已新增，章节数：{}========>",
                                            tokenName, novelId, novelChapterIds.size());
                                }
                            } catch (Exception e) {
                                log.error("<==========【{}】小说【{}】更新数据库记录失败：{}========>",
                                        tokenName, novelId, e.getMessage(), e);
                                // 虽然数据库更新失败，但文件已上传成功，仍然可以返回URL
                            }
                        }

                        // 更新任务状态为完成
                        updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, fileUrl, null);

                        log.info("<==========【{}】小说【{}】异步下载完成，文件URL：{}========>",
                                tokenName, novelId, fileUrl);

                        // 删除临时文件
                        if (tempFile != null) {
                            try {
                                Files.delete(tempFile);
                            } catch (IOException e) {
                                log.warn("<==========【{}】小说【{}】删除临时文件失败：{}========>",
                                        tokenName, novelId, e.getMessage());
                            }
                        }

                    } catch (IOException e) {
                        log.error("<==========【{}】小说【{}】创建文件失败：{}========>",
                                tokenName, novelId, e.getMessage(), e);
                        updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "文件创建失败：" + e.getMessage());

                        // 删除临时文件
                        if (tempFile != null) {
                            try {
                                Files.delete(tempFile);
                            } catch (IOException ex) {
                                log.warn("<==========【{}】小说【{}】删除临时文件失败：{}========>",
                                        tokenName, novelId, ex.getMessage());
                            }
                        }
                    }
                } else {
                    log.error("<==========【{}】小说【{}】内容为空========>", tokenName, novelId);
                    updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, "小说内容为空");
                }
            } else {
                // 不需要处理章节，直接标记为完成
                updateTaskStatus(taskId, TASK_STATUS_COMPLETED, 100, null, null);
                log.info("<==========【{}】小说【{}】任务完成，无需下载章节========>", tokenName, novelId);
            }
        } catch (Exception e) {
            log.error("<==========【{}】小说【{}】异步下载失败：{}========>",
                    tokenName, novelId, e.getMessage(), e);
            updateTaskStatus(taskId, TASK_STATUS_FAILED, 0, null, e.getMessage());
        } finally {
            // 无论成功还是失败，都释放小说下载锁
            redisTemplate.delete(novelTaskKey);
        }
    }

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param progress 进度
     * @param fileUrl 文件URL
     * @param errorMsg 错误信息
     */
    private void updateTaskStatus(String taskId, String status, int progress, String fileUrl, String errorMsg) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;

        Map<String, Object> updates = new HashMap<>();
        updates.put("status", status);
        updates.put("progress", progress);
        updates.put("updateTime", new Date());

        if (fileUrl != null) {
            updates.put("fileUrl", fileUrl);
        }

        if (errorMsg != null) {
            updates.put("errorMsg", errorMsg);
        }

        redisTemplate.opsForHash().putAll(redisKey, updates);
        redisTemplate.expire(redisKey, TASK_EXPIRE_HOURS, TimeUnit.HOURS);
    }

    /**
     * 查询任务状态
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/query_task")
    public AjaxResult queryTask(@RequestParam String taskId) {
        String redisKey = REDIS_TASK_KEY_PREFIX + taskId;

        // 检查任务是否存在
        if (!redisTemplate.hasKey(redisKey)) {
            return AjaxResult.error("任务不存在或已过期");
        }

        // 获取任务信息
        Map<Object, Object> taskInfo = redisTemplate.opsForHash().entries(redisKey);


        return AjaxResult.success(taskInfo);
    }

    /**
     * 处理番茄小说章节内容 - 优化版本，使用POST批量请求
     *
     * @param taskId 任务ID，用于更新进度
     * @param novelId 小说ID
     * @param novelName 小说名称
     * @param novelAuthor 小说作者
     * @param novelDesc 小说描述
     * @param chapterIds 章节ID数组
     * @param chapterTitles 章节标题数组
     * @return 组装好的小说内容
     */
    private String processFanqieChapters(String taskId, String novelId, String novelName, String novelAuthor, String novelDesc, JSONArray chapterIds, JSONArray chapterTitles) {
        // 添加小说头部信息
        StringBuilder novelHeader = new StringBuilder();
        novelHeader.append("书名：").append(novelName).append("\r\n\r\n");
        novelHeader.append("作者：").append(novelAuthor).append("\r\n\r\n");
        novelHeader.append("简介：").append(novelDesc).append("\r\n\r\n");
        novelHeader.append("====================\r\n\r\n");

        // 将章节ID和标题拼接成对象数组
        JSONArray chapterInfoArray = new JSONArray();
        int totalChapters = chapterIds.size();

        for (int i = 0; i < totalChapters; i++) {
            JSONObject chapterInfo = new JSONObject();
            chapterInfo.put("id", chapterIds.getStr(i));

            // 获取章节标题，如果标题数组存在且索引有效则使用，否则使用默认标题
            String chapterTitle = (chapterTitles != null && i < chapterTitles.size())
                ? chapterTitles.getStr(i)
                : "第" + (i + 1) + "章";
            chapterInfo.put("title", chapterTitle);
            chapterInfo.put("index", i); // 添加索引，确保按顺序组装

            chapterInfoArray.add(chapterInfo);
        }

        // 计算进度相关变量
        AtomicInteger completedChapters = new AtomicInteger(0);
        AtomicInteger failedChapters = new AtomicInteger(0);
        AtomicInteger lastProgressReported = new AtomicInteger(20); // 初始进度为20%

        // 创建一个线程安全的Map来存储章节内容，键为章节索引，值为章节内容
        ConcurrentHashMap<Integer, String> chapterContents = new ConcurrentHashMap<>();

        // 创建一个线程安全的Set来跟踪失败的章节
        Set<Integer> failedChapterIndices = Collections.synchronizedSet(new HashSet<>());

        // 优化批量大小设置 - 使用POST批量请求，每次最多300个章节
        final int BATCH_SIZE = 300; // 每批最多300个章节ID
        final int MAX_CONCURRENT_THREADS = 3; // 减少并发线程数，因为使用批量请求

        // 添加任务超时检测
        final long startTime = System.currentTimeMillis();
        final long MAX_TASK_TIME = 10 * 60 * 1000; // 最大任务时间10分钟

        // 添加任务中断标志
        final AtomicBoolean taskInterrupted = new AtomicBoolean(false);

        log.info("开始下载小说《{}》，总章节数：{}，使用POST批量请求模式", novelName, totalChapters);

        // 创建固定大小的线程池
        ExecutorService executor = Executors.newFixedThreadPool(MAX_CONCURRENT_THREADS);
        List<Future<?>> futures = new ArrayList<>();

        // 将章节ID分批处理
        for (int batchStart = 0; batchStart < totalChapters && !taskInterrupted.get(); batchStart += BATCH_SIZE) {
            // 检查任务是否超时
            if (System.currentTimeMillis() - startTime > MAX_TASK_TIME) {
                log.warn("小说下载任务超时，已执行{}分钟，强制结束任务", (System.currentTimeMillis() - startTime) / 60000);
                taskInterrupted.set(true);
                break;
            }

            final int currentBatchStart = batchStart;
            final int currentBatchEnd = Math.min(batchStart + BATCH_SIZE, totalChapters);

            // 创建异步任务处理当前批次
            Future<?> future = executor.submit(() -> {
                // 检查任务是否已中断
                if (taskInterrupted.get()) {
                    return;
                }

                // 处理当前批次的章节
                processBatchChapters(taskId, novelId, currentBatchStart, currentBatchEnd, chapterInfoArray,
                        chapterContents, failedChapterIndices, completedChapters,
                        lastProgressReported, totalChapters, taskInterrupted);
            });

            futures.add(future);

            // 添加批次间的延迟，避免请求过于密集
            if (batchStart > 0) {
                try {
                    Thread.sleep(200 + new Random().nextInt(300));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    taskInterrupted.set(true);
                    break;
                }
            }
        }

        // 等待所有批次完成或超时
        try {
            executor.shutdown();
            if (!executor.awaitTermination(5, TimeUnit.MINUTES)) {
                log.warn("等待章节下载超时，强制关闭线程池");
                executor.shutdownNow();
                taskInterrupted.set(true);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("章节下载被中断");
            executor.shutdownNow();
            taskInterrupted.set(true);
        }

        // 如果任务被中断，记录日志
        if (taskInterrupted.get()) {
            log.warn("小说《{}》下载任务被中断，已完成{}/{}章节", novelName, completedChapters.get(), totalChapters);
        }

        // 处理失败的章节（二次补偿机制）- 使用单个请求重试
        if (!failedChapterIndices.isEmpty() && !taskInterrupted.get() && failedChapterIndices.size() <= totalChapters * 0.2) {
            log.info("开始尝试重新获取{}个失败章节", failedChapterIndices.size());
            retryFailedChaptersIndividually(failedChapterIndices, chapterInfoArray, chapterContents);
        }

        // 确保最终进度达到60%
        if (lastProgressReported.get() < 60) {
            updateTaskStatus(taskId, TASK_STATUS_PROCESSING, 60, null, null);
        }

        // 组装最终的小说内容
        StringBuilder novelContent = new StringBuilder(novelHeader);

        // 按chapterIds数组顺序添加章节内容
        for (int i = 0; i < totalChapters; i++) {
            String chapterContent = chapterContents.getOrDefault(i, null);

            if (chapterContent != null) {
                novelContent.append(chapterContent);
            } else {
                // 对于仍然缺失的章节，从章节信息数组中获取标题并添加占位符
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
                String chapterTitle = chapterInfo.getStr("title");
                novelContent.append(chapterTitle).append("\r\n\r\n");
                novelContent.append("[章节内容获取失败，请稍后重试]\r\n\r\n");
                failedChapters.incrementAndGet();
            }
        }

        // 记录章节获取情况
        log.info("小说《{}》下载完成，总章节数：{}，成功获取：{}，失败：{}，耗时：{}秒",
                novelName, totalChapters, totalChapters - failedChapters.get(), failedChapters.get(),
                (System.currentTimeMillis() - startTime) / 1000);

        return novelContent.toString();
    }

    /**
     * 从小说ID或章节ID中提取book_id
     * @param novelId 小说ID
     * @param chapterIds 章节ID数组
     * @return book_id
     */
    private String extractBookId(String novelId, JSONArray chapterIds) {
        // 方法1: 如果novelId就是book_id格式，直接使用
        if (novelId != null && novelId.length() > 10 && novelId.matches("\\d+")) {
            return novelId;
        }

        // 方法2: 从章节ID中提取book_id（假设章节ID包含book信息）
        if (chapterIds != null && chapterIds.size() > 0) {
            String firstChapterId = chapterIds.getStr(0);
            if (firstChapterId != null && firstChapterId.length() > 10) {
                // 如果章节ID格式为 "bookId_chapterId" 或类似格式
                if (firstChapterId.contains("_")) {
                    String[] parts = firstChapterId.split("_");
                    if (parts.length > 0 && parts[0].matches("\\d+")) {
                        return parts[0];
                    }
                }
                // 如果章节ID本身就是数字格式，可能需要其他逻辑来推导book_id
                // 这里暂时使用一个默认值，实际使用时需要根据具体的ID格式调整
            }
        }

        // 方法3: 使用默认的book_id（需要根据实际情况调整）
        log.warn("无法从novelId: {} 或章节ID中提取book_id，使用默认值", novelId);
        return "7416289834324479001"; // 默认book_id，需要根据实际情况调整
    }

    /**
     * 从小说ID或单个章节ID中提取book_id
     * @param novelId 小说ID
     * @param chapterId 单个章节ID
     * @return book_id
     */
    private String extractBookId(String novelId, String chapterId) {
        // 方法1: 如果novelId就是book_id格式，直接使用
        if (novelId != null && novelId.length() > 10 && novelId.matches("\\d+")) {
            return novelId;
        }

        // 方法2: 从章节ID中提取book_id（假设章节ID包含book信息）
        if (chapterId != null && chapterId.length() > 10) {
            // 如果章节ID格式为 "bookId_chapterId" 或类似格式
            if (chapterId.contains("_")) {
                String[] parts = chapterId.split("_");
                if (parts.length > 0 && parts[0].matches("\\d+")) {
                    return parts[0];
                }
            }
            // 如果章节ID本身就是数字格式，可能需要其他逻辑来推导book_id
            // 这里暂时使用一个默认值，实际使用时需要根据具体的ID格式调整
        }

        // 方法3: 使用默认的book_id（需要根据实际情况调整）
        log.warn("无法从novelId: {} 或章节ID: {} 中提取book_id，使用默认值", novelId, chapterId);
        return "7416289834324479001"; // 默认book_id，需要根据实际情况调整
    }

    /**
     * 处理批量章节 - 使用POST请求批量获取章节内容
     * @param taskId 任务ID
     * @param novelId 小说ID
     * @param batchStart 批次开始索引
     * @param batchEnd 批次结束索引
     * @param chapterInfoArray 章节信息数组（包含id、title、index）
     * @param chapterContents 章节内容映射
     * @param failedChapterIndices 失败章节索引集合
     * @param completedChapters 已完成章节计数器
     * @param lastProgressReported 最后报告的进度
     * @param totalChapters 总章节数
     * @param taskInterrupted 任务中断标志
     */
    private void processBatchChapters(String taskId, String novelId, int batchStart, int batchEnd, JSONArray chapterInfoArray,
                                      ConcurrentHashMap<Integer, String> chapterContents,
                                      Set<Integer> failedChapterIndices, AtomicInteger completedChapters,
                                      AtomicInteger lastProgressReported, int totalChapters, AtomicBoolean taskInterrupted) {

        // 提取当前批次的章节ID
        List<String> batchChapterIds = new ArrayList<>();

        for (int i = batchStart; i < batchEnd; i++) {
            JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
            String chapterId = chapterInfo.getStr("id");
            batchChapterIds.add(chapterId);
        }

        // 使用新的方法提取book_id，从第一个章节信息中获取ID
        JSONObject firstChapterInfo = chapterInfoArray.getJSONObject(batchStart);
        String firstChapterId = firstChapterInfo.getStr("id");
        String bookId = extractBookId(novelId, firstChapterId);

        if (bookId == null) {
            log.error("无法获取book_id，批次处理失败");
            // 将当前批次的所有章节标记为失败
            for (int i = batchStart; i < batchEnd; i++) {
                failedChapterIndices.add(i);
            }
            return;
        }

        int retryCount = 0;
        boolean batchSuccess = false;
        final int maxRetries = 3;
        final int baseWaitTime = 500;

        while (!batchSuccess && retryCount < maxRetries && !taskInterrupted.get()) {
            long startTime = System.currentTimeMillis();

            try {
                // 构建POST请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("book_id", bookId);
                requestBody.put("item_ids", batchChapterIds);

                log.debug("发送批量请求，book_id: {}, 章节数量: {}, 批次: {}-{}",
                        bookId, batchChapterIds.size(), batchStart, batchEnd - 1);

                // 发送POST请求
                cn.hutool.http.HttpRequest request = cn.hutool.http.HttpUtil.createPost(FANQIE_URL)
                        .header("User-Agent", getRandomUserAgent())
                        .header("Accept", "application/json, text/plain, */*")
                        .header("Content-Type", "application/json")
                        .header("Accept-Encoding", "gzip, deflate, br")
                        .header("Connection", "keep-alive")
                        .body(requestBody.toString())
                        .timeout(15000); // 批量请求使用更长的超时时间

                // 添加随机性，避免被反爬
                if (retryCount > 0) {
                    request.header("X-Retry-Count", String.valueOf(retryCount));
                    request.header("User-Agent", getRandomUserAgent());
                    if (new Random().nextBoolean()) {
                        request.header("Referer", "https://fanqienovel.com/");
                    }
                }

                // 执行请求
                cn.hutool.http.HttpResponse response;
                try {
                    response = request.execute();
                } catch (cn.hutool.http.HttpException e) {
                    log.warn("批量获取章节内容HTTP请求异常，批次: {}-{}, 重试次数: {}, 异常: {}",
                            batchStart, batchEnd - 1, retryCount, e.getMessage());
                    retryCount++;
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                    Thread.sleep(waitTime);
                    continue;
                }

                if (response.isOk()) {
                    String responseBody = response.body();

                    // 快速检查响应是否有效
                    if (responseBody == null || responseBody.trim().isEmpty()) {
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                        Thread.sleep(waitTime);
                        continue;
                    }

                    // 解析批量响应
                    try {
                        JSONArray dataObj = new JSONArray(responseBody);

                        if (!dataObj.isEmpty()) {

                            // 处理批量响应数据
                            processBatchResponseArray(dataObj, batchStart, batchEnd, chapterInfoArray,
                                    chapterContents, failedChapterIndices, completedChapters);

                            batchSuccess = true;

                            // 更新进度
                            int completed = completedChapters.get();
                            int currentProgress = 20 + (int)((float)completed / totalChapters * 40);

                            if (currentProgress >= lastProgressReported.get() + 5) {
                                synchronized (this) {
                                    if (currentProgress >= lastProgressReported.get() + 5) {
                                        updateTaskStatus(taskId, TASK_STATUS_PROCESSING, currentProgress, null, null);
                                        lastProgressReported.set(currentProgress);
                                        log.debug("批量下载进度更新：{}/{}章节，进度{}%", completed, totalChapters, currentProgress);
                                    }
                                }
                            }

                            log.info("批量请求成功，批次: {}-{}, 耗时: {}ms",
                                    batchStart, batchEnd - 1, System.currentTimeMillis() - startTime);
                            break;
                        }

                        // 如果执行到这里，说明数据提取失败
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                        Thread.sleep(waitTime);

                    } catch (Exception e) {
                        log.warn("批量请求JSON解析失败，批次: {}-{}, 重试次数: {}, 异常: {}",
                                batchStart, batchEnd - 1, retryCount, e.getMessage());
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                        Thread.sleep(waitTime);
                    }
                } else {
                    log.warn("批量请求HTTP失败，批次: {}-{}, 状态码: {}, 重试次数: {}",
                            batchStart, batchEnd - 1, response.getStatus(), retryCount);
                    retryCount++;
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                    Thread.sleep(waitTime);
                }
            } catch (Exception e) {
                log.error("批量请求异常，批次: {}-{}, 重试次数: {}, 异常: {}",
                        batchStart, batchEnd - 1, retryCount, e.getMessage());
                retryCount++;
                try {
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 3)), 3000);
                    Thread.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    taskInterrupted.set(true);
                    break;
                }
            }

            // 检查总执行时间，如果超过30秒则放弃此批次
            if (System.currentTimeMillis() - startTime > 30000) {
                log.warn("批量请求超时（已执行{}ms），放弃批次: {}-{}",
                        System.currentTimeMillis() - startTime, batchStart, batchEnd - 1);
                break;
            }
        }

        // 如果批次处理失败，将所有章节标记为失败
        if (!batchSuccess) {
            for (int i = batchStart; i < batchEnd; i++) {
                failedChapterIndices.add(i);
            }
            log.warn("批量请求失败（已重试{}次），批次: {}-{}", retryCount, batchStart, batchEnd - 1);
        }
    }

    /**
     * 处理批量响应数据（数组格式）
     * @param dataArray 响应数据数组
     * @param batchStart 批次开始索引
     * @param batchEnd 批次结束索引
     * @param chapterInfoArray 章节信息数组（包含id、title、index）
     * @param chapterContents 章节内容映射
     * @param failedChapterIndices 失败章节索引集合
     * @param completedChapters 已完成章节计数器
     */
    private void processBatchResponseArray(JSONArray dataArray, int batchStart, int batchEnd, JSONArray chapterInfoArray,
                                           ConcurrentHashMap<Integer, String> chapterContents,
                                           Set<Integer> failedChapterIndices, AtomicInteger completedChapters) {

        // 检查响应格式是否为新格式 (只包含title和content，没有item_id)
        boolean isNewFormat = false;
        if (dataArray.size() > 0) {
            JSONObject firstItem = dataArray.getJSONObject(0);
            if (firstItem.containsKey("title") && firstItem.containsKey("content") && !firstItem.containsKey("item_id")) {
                isNewFormat = true;
                log.info("检测到新响应格式，使用索引顺序匹配章节");
            }
        }

        if (isNewFormat) {
            // 新格式: 使用数组索引顺序匹配章节
            int maxIndex = Math.min(dataArray.size(), batchEnd - batchStart);

            for (int i = 0; i < maxIndex; i++) {
                try {
                    JSONObject chapterData = dataArray.getJSONObject(i);
                    String title = chapterData.getStr("title");
                    String content = chapterData.getStr("content");

                    if (content != null && !content.isEmpty()) {
                        // 计算实际章节索引
                        int chapterIndex = batchStart + i;

                        // 从章节信息数组中获取章节标题 (优先使用响应中的标题)
                        String chapterTitle = title;
                        // if (chapterTitle == null || chapterTitle.isEmpty()) {
                            
                        // }
                        // 如果响应中的标题为空，从章节信息数组中获取
                            if (chapterIndex < chapterInfoArray.size()) {
                                JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
                                chapterTitle = chapterInfo.getStr("title");
                            } else {
                                chapterTitle = "第" + (chapterIndex + 1) + "章";
                            }

                        // 组装章节内容
                        String formattedChapter = chapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                        // 存储章节内容
                        chapterContents.put(chapterIndex, formattedChapter);
                        completedChapters.incrementAndGet();
                    }
                } catch (Exception e) {
                    log.warn("处理批量响应中的章节数据失败，索引: {}, 异常: {}", i, e.getMessage());

                    // 标记对应章节为失败
                    failedChapterIndices.add(batchStart + i);
                }
            }

            // 如果响应章节数小于请求章节数，标记剩余章节为失败
            for (int i = dataArray.size(); i < (batchEnd - batchStart); i++) {
                failedChapterIndices.add(batchStart + i);
            }
        } else {
            // 原格式: 使用章节ID匹配
            // 创建章节ID到索引的映射，便于快速查找
            Map<String, Integer> chapterIdToIndex = new HashMap<>();
            for (int i = batchStart; i < batchEnd; i++) {
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
                String chapterId = chapterInfo.getStr("id");
                chapterIdToIndex.put(chapterId, i);
            }

            // 处理响应中的每个章节
            for (int i = 0; i < dataArray.size(); i++) {
                try {
                    JSONObject chapterData = dataArray.getJSONObject(i);
                    String chapterId = chapterData.getStr("item_id");
                    String content = chapterData.getStr("content");

                    if (chapterId != null && content != null && !content.isEmpty()) {
                        Integer chapterIndex = chapterIdToIndex.get(chapterId);

                        if (chapterIndex != null) {
                            // 从章节信息数组中获取章节标题
                            String chapterTitle;
                            if (chapterIndex < chapterInfoArray.size()) {
                                JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
                                chapterTitle = chapterInfo.getStr("title");
                            } else {
                                chapterTitle = chapterData.getStr("title", "第" + (chapterIndex + 1) + "章");
                            }

                            // 组装章节内容
                            String formattedChapter = chapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                            // 存储章节内容
                            chapterContents.put(chapterIndex, formattedChapter);
                            completedChapters.incrementAndGet();

                            // 从映射中移除已处理的章节
                            chapterIdToIndex.remove(chapterId);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理批量响应中的章节数据失败，索引: {}, 异常: {}", i, e.getMessage());
                }
            }

            // 将未成功处理的章节标记为失败
            for (Integer failedIndex : chapterIdToIndex.values()) {
                failedChapterIndices.add(failedIndex);
            }
        }
    }

    /**
     * 处理批量响应数据（对象格式）
     * @param dataObject 响应数据对象
     * @param batchStart 批次开始索引
     * @param batchEnd 批次结束索引
     * @param chapterInfoArray 章节信息数组（包含id、title、index）
     * @param chapterContents 章节内容映射
     * @param failedChapterIndices 失败章节索引集合
     * @param completedChapters 已完成章节计数器
     */
    private void processBatchResponseObject(JSONObject dataObject, int batchStart, int batchEnd, JSONArray chapterInfoArray,
                                            ConcurrentHashMap<Integer, String> chapterContents,
                                            Set<Integer> failedChapterIndices, AtomicInteger completedChapters) {

        // 检查是否为新格式数据（数组格式但被包装在对象中）
        boolean isNewFormatWrapped = false;
        for (String key : dataObject.keySet()) {
            Object value = dataObject.getObj(key);
            if (value instanceof JSONArray) {
                JSONArray arr = (JSONArray) value;
                if (arr.size() > 0) {
                    JSONObject firstItem = arr.getJSONObject(0);
                    if (firstItem.containsKey("title") && firstItem.containsKey("content") && !firstItem.containsKey("item_id")) {
                        isNewFormatWrapped = true;
                        log.info("检测到包装在对象中的新响应格式数组，key={}", key);

                        // 提取数组并处理
                        processBatchResponseArray(arr, batchStart, batchEnd, chapterInfoArray,
                                chapterContents, failedChapterIndices, completedChapters);
                        return;
                    }
                }
            }
        }

        // 创建章节ID到索引的映射
        Map<String, Integer> chapterIdToIndex = new HashMap<>();
        for (int i = batchStart; i < batchEnd; i++) {
            JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
            String chapterId = chapterInfo.getStr("id");
            chapterIdToIndex.put(chapterId, i);
        }

        // 遍历响应对象中的所有键值对
        for (String chapterId : dataObject.keySet()) {
            try {
                Object chapterDataObj = dataObject.getObj(chapterId);

                if (chapterDataObj instanceof JSONObject) {
                    JSONObject chapterData = (JSONObject) chapterDataObj;
                    String content = chapterData.getStr("content");

                    if (content != null && !content.isEmpty()) {
                        Integer chapterIndex = chapterIdToIndex.get(chapterId);

                        if (chapterIndex != null) {
                            // 从章节信息数组中获取章节标题
                            String chapterTitle;
                            if (chapterIndex < chapterInfoArray.size()) {
                                JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
                                chapterTitle = chapterInfo.getStr("title");
                            } else {
                                chapterTitle = chapterData.getStr("title", "第" + (chapterIndex + 1) + "章");
                            }

                            // 组装章节内容
                            String formattedChapter = chapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                            // 存储章节内容
                            chapterContents.put(chapterIndex, formattedChapter);
                            completedChapters.incrementAndGet();

                            // 从映射中移除已处理的章节
                            chapterIdToIndex.remove(chapterId);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("处理批量响应中的章节数据失败，章节ID: {}, 异常: {}", chapterId, e.getMessage());
            }
        }

        // 将未成功处理的章节标记为失败
        for (Integer failedIndex : chapterIdToIndex.values()) {
            failedChapterIndices.add(failedIndex);
        }
    }

    /**
     * 处理单个章节
     * @param chapterIndex 章节索引
     * @param chapterId 章节ID
     * @param chapterTitles 章节标题数组
     * @param chapterContents 章节内容映射
     * @param failedChapterIndices 失败章节索引集合
     */
    private void processChapter(int chapterIndex, String chapterId, JSONArray chapterTitles,
                                ConcurrentHashMap<Integer, String> chapterContents,
                                Set<Integer> failedChapterIndices) {
        int retryCount = 0;
        boolean chapterSuccess = false;

        // 优化重试策略
        int maxRetries = 2; // 减少重试次数以加快整体下载速度
        int baseWaitTime = 300; // 减少基础等待时间
        int maxRequestTime = 8000; // 减少最大请求时间以更快失败

        while (!chapterSuccess && retryCount < maxRetries) {
            long startTime = System.currentTimeMillis();
            try {
                // 构建POST请求体 - 单个章节
                JSONObject requestBody = new JSONObject();
                requestBody.put("book_id", "7416289834324479001"); // 暂时使用默认值，processChapter方法需要传入更多参数才能使用extractBookId
                requestBody.put("item_ids", Collections.singletonList(chapterId));

                // 计算当前重试的超时时间
                int currentTimeout = Math.min(4000 + retryCount * 1500, maxRequestTime);

                // 发送HTTP POST请求获取章节内容
                cn.hutool.http.HttpRequest request = cn.hutool.http.HttpUtil.createPost(FANQIE_URL)
                        .header("User-Agent", getRandomUserAgent())
                        .header("Accept", "application/json, text/plain, */*")
                        .header("Content-Type", "application/json")
                        .header("Accept-Encoding", "gzip, deflate, br")
                        .header("Connection", "keep-alive")
                        .body(requestBody.toString())
                        .timeout(currentTimeout);

                // 设置连接超时和读取超时
                request.setConnectionTimeout(3000); // 减少连接超时
                request.setReadTimeout(currentTimeout);

                // 添加随机性，避免被反爬
                if (retryCount > 0) {
                    request.header("X-Retry-Count", String.valueOf(retryCount));
                    request.header("User-Agent", getRandomUserAgent());
                    // 添加随机请求头
                    if (new Random().nextBoolean()) {
                        request.header("Referer", "https://fanqienovel.com/");
                    }
                }

                // 执行请求
                cn.hutool.http.HttpResponse response;
                try {
                    if (System.currentTimeMillis() - startTime > maxRequestTime) {
                        throw new cn.hutool.http.HttpException("请求执行前已超时");
                    }

                    response = request.execute();

                    if (System.currentTimeMillis() - startTime > maxRequestTime) {
                        throw new cn.hutool.http.HttpException("请求执行后已超时");
                    }
                } catch (cn.hutool.http.HttpException e) {
                    log.warn("获取章节内容HTTP请求异常，章节ID：{}，重试次数：{}，异常：{}",
                            chapterId, retryCount, e.getMessage());
                    retryCount++;
                    // 使用更短的等待时间
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                    Thread.sleep(waitTime);
                    continue;
                }

                if (response.isOk()) {
                    String responseBody = response.body();

                    // 快速检查响应是否有效
                    if (responseBody == null || responseBody.trim().isEmpty() ||
                            !responseBody.trim().startsWith("{")) {
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                        Thread.sleep(waitTime);
                        continue;
                    }

                    // 使用try-catch块包装JSON解析和内容提取 - 适配POST批量响应格式
                    try {
                        JSONObject jsonObject = new JSONObject(responseBody);

                        if (jsonObject.containsKey("data") && jsonObject.getObj("data") != null) {
                            Object dataObj = jsonObject.getObj("data");
                            String content = null;
                            String title = null;

                            // 处理不同的响应格式
                            if (dataObj instanceof JSONArray) {
                                // 如果返回的是数组格式
                                JSONArray dataArray = (JSONArray) dataObj;
                                if (dataArray.size() > 0) {
                                    JSONObject chapterData = dataArray.getJSONObject(0);
                                    content = chapterData.getStr("content");
                                    title = chapterData.getStr("title");
                                }
                            } else if (dataObj instanceof JSONObject) {
                                // 如果返回的是对象格式
                                JSONObject dataObject = (JSONObject) dataObj;

                                // 尝试直接获取content（兼容旧格式）
                                if (dataObject.containsKey("content")) {
                                    content = dataObject.getStr("content");
                                    title = dataObject.getStr("title");
                                } else {
                                    // 遍历对象中的章节数据
                                    for (String key : dataObject.keySet()) {
                                        Object chapterDataObj = dataObject.getObj(key);
                                        if (chapterDataObj instanceof JSONObject) {
                                            JSONObject chapterData = (JSONObject) chapterDataObj;
                                            content = chapterData.getStr("content");
                                            title = chapterData.getStr("title");
                                            break;
                                        }
                                    }
                                }
                            }

                            if (content != null && !content.isEmpty()) {
                                // 获取章节标题
                                String chapterTitle;
                                if (chapterTitles != null && chapterIndex < chapterTitles.size()) {
                                    chapterTitle = chapterTitles.getStr(chapterIndex);
                                } else {
                                    chapterTitle = title != null ? title : "第" + (chapterIndex + 1) + "章";
                                }

                                // 组装章节内容
                                String formattedChapter = chapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                                // 存储章节内容
                                chapterContents.put(chapterIndex, formattedChapter);
                                chapterSuccess = true;

                                // 只在重试时记录日志，减少日志量
                                if (retryCount > 0) {
                                    log.info("章节内容获取成功（重试{}次后），章节ID：{}", retryCount, chapterId);
                                }
                                break; // 成功获取内容，退出循环
                            }
                        }

                        // 如果执行到这里，说明数据提取失败
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                        Thread.sleep(waitTime);

                    } catch (Exception e) {
                        // JSON解析失败
                        retryCount++;
                        int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                        Thread.sleep(waitTime);
                    }
                } else {
                    // HTTP请求失败
                    retryCount++;
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                    Thread.sleep(waitTime);
                }
            } catch (Exception e) {
                // 其他异常
                retryCount++;
                try {
                    int waitTime = Math.min(baseWaitTime * (1 << Math.min(retryCount, 2)), 1000);
                    Thread.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }

            // 检查总执行时间，如果超过15秒则放弃此章节（减少超时时间）
            if (System.currentTimeMillis() - startTime > 15000) {
                log.warn("章节下载超时（已执行{}ms），放弃下载，章节ID：{}",
                        System.currentTimeMillis() - startTime, chapterId);
                break;
            }
        }

        // 如果章节下载失败，记录下来
        if (!chapterSuccess) {
            failedChapterIndices.add(chapterIndex);
            log.warn("章节下载失败（已重试{}次），章节索引：{}，章节ID：{}", retryCount, chapterIndex, chapterId);
        }
    }

    /**
     * 重试下载失败的章节 - 使用单个POST请求逐个重试
     * @param failedChapterIndices 失败章节索引集合
     * @param chapterInfoArray 章节信息数组（包含id、title、index）
     * @param chapterContents 章节内容映射
     */
    private void retryFailedChaptersIndividually(Set<Integer> failedChapterIndices, JSONArray chapterInfoArray,
                                                 ConcurrentHashMap<Integer, String> chapterContents) {
        // 转换为列表以便遍历，并按章节索引排序
        List<Integer> failedIndices = new ArrayList<>(failedChapterIndices);
        Collections.sort(failedIndices);

        // 设置重试的最大数量，避免过多重试
        int maxRetryCount = Math.min(failedIndices.size(), 20);

        if (failedIndices.size() > maxRetryCount) {
            log.info("失败章节过多({}个)，只重试前{}个章节", failedIndices.size(), maxRetryCount);
            failedIndices = failedIndices.subList(0, maxRetryCount);
        }

        // 设置重试超时时间
        final long startTime = System.currentTimeMillis();
        final long MAX_RETRY_TIME = 120 * 1000; // 最大重试时间2分钟

        // 创建线程池，使用较少的并发以减少被反爬概率
        int parallelism = Math.min(failedIndices.size(), 2);
        ExecutorService executor = Executors.newFixedThreadPool(parallelism);
        List<Future<?>> futures = new ArrayList<>();

        // 创建计数器跟踪成功恢复的章节数
        AtomicInteger recoveredCount = new AtomicInteger(0);

        // 提交所有重试任务
        for (Integer index : failedIndices) {
            // 检查是否超时
            if (System.currentTimeMillis() - startTime > MAX_RETRY_TIME) {
                log.warn("重试失败章节超时，已执行{}秒，强制结束重试", (System.currentTimeMillis() - startTime) / 1000);
                break;
            }

            futures.add(executor.submit(() -> {
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(index);
                String chapterId = chapterInfo.getStr("id");
                String chapterTitle = chapterInfo.getStr("title");

                try {
                    // 使用递增延迟，避免请求过于集中
                    Thread.sleep(500 + (index % 5) * 200);

                    // 构建POST请求体 - 单个章节重试
                    JSONObject requestBody = new JSONObject();
                    requestBody.put("book_id", extractBookId(null, chapterId)); // 使用提取方法获取book_id
                    requestBody.put("item_ids", Collections.singletonList(chapterId));

                    // 使用POST请求重试单个章节
                    cn.hutool.http.HttpRequest request = cn.hutool.http.HttpUtil.createPost(FANQIE_URL)
                            .header("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1")
                            .header("Accept", "application/json, text/plain, */*")
                            .header("Content-Type", "application/json")
                            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                            .header("Accept-Encoding", "gzip, deflate, br")
                            .header("Connection", "keep-alive")
                            .header("Referer", "https://fanqienovel.com/")
                            .body(requestBody.toString())
                            .timeout(10000);

                    // 执行请求
                    cn.hutool.http.HttpResponse response;
                    try {
                        response = request.execute();
                    } catch (Exception e) {
                        log.warn("重试章节请求失败，章节索引: {}, 章节ID: {}, 异常: {}", index, chapterId, e.getMessage());
                        return null;
                    }

                    if (response.isOk()) {
                        String responseBody = response.body();

                        try {
                            JSONObject jsonObject = new JSONObject(responseBody);

                            if (jsonObject.containsKey("data") && jsonObject.getObj("data") != null) {
                                Object dataObj = jsonObject.getObj("data");
                                String content = null;
                                String title = null;

                                // 处理不同的响应格式
                                if (dataObj instanceof JSONArray) {
                                    JSONArray dataArray = (JSONArray) dataObj;
                                    if (dataArray.size() > 0) {
                                        JSONObject chapterData = dataArray.getJSONObject(0);
                                        content = chapterData.getStr("content");
                                        title = chapterData.getStr("title");
                                    }
                                } else if (dataObj instanceof JSONObject) {
                                    JSONObject dataObject = (JSONObject) dataObj;
                                    // 如果是对象格式，尝试获取第一个章节的内容
                                    for (String key : dataObject.keySet()) {
                                        Object chapterDataObj = dataObject.getObj(key);
                                        if (chapterDataObj instanceof JSONObject) {
                                            JSONObject chapterData = (JSONObject) chapterDataObj;
                                            content = chapterData.getStr("content");
                                            title = chapterData.getStr("title");
                                            break;
                                        }
                                    }
                                }

                                if (content != null && !content.isEmpty()) {
                                    // 获取章节标题（优先使用响应中的标题，其次使用章节信息数组中的标题）
                                    String finalChapterTitle = (title != null && !title.isEmpty()) ? title : chapterTitle;

                                    // 组装章节内容
                                    String formattedChapter = finalChapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                                    // 存储章节内容
                                    chapterContents.put(index, formattedChapter);

                                    // 从失败集合中移除
                                    synchronized (failedChapterIndices) {
                                        failedChapterIndices.remove(index);
                                    }

                                    // 增加恢复计数
                                    recoveredCount.incrementAndGet();
                                    log.info("重试成功恢复章节，索引: {}, 章节ID: {}", index, chapterId);
                                    return true;
                                }
                            }
                        } catch (Exception e) {
                            log.warn("重试章节响应解析失败，章节索引: {}, 章节ID: {}, 异常: {}", index, chapterId, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.warn("重试章节异常，章节索引: {}, 章节ID: {}, 异常: {}", index, chapterId, e.getMessage());
                }

                return null;
            }));
        }

        // 等待所有重试任务完成或超时
        try {
            executor.shutdown();
            if (!executor.awaitTermination(Math.min(MAX_RETRY_TIME - (System.currentTimeMillis() - startTime), 90000), TimeUnit.MILLISECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 记录重试结果
        log.info("章节重试完成，尝试重试{}章节，成功恢复{}章节，耗时{}秒",
                failedIndices.size(), recoveredCount.get(), (System.currentTimeMillis() - startTime) / 1000);
    }

    /**
     * 重试下载失败的章节 - 原有的GET请求方式（备用）
     * @param failedChapterIndices 失败章节索引集合
     * @param chapterIds 章节ID数组
     * @param chapterTitles 章节标题数组
     * @param chapterContents 章节内容映射
     */
    private void retryFailedChapters(Set<Integer> failedChapterIndices, JSONArray chapterIds,
                                     JSONArray chapterTitles, ConcurrentHashMap<Integer, String> chapterContents) {
        // 转换为列表以便遍历，并按章节索引排序
        List<Integer> failedIndices = new ArrayList<>(failedChapterIndices);
        Collections.sort(failedIndices); // 按章节顺序处理，提高效率

        // 设置重试的最大数量，避免过多重试
        int maxRetryCount = Math.min(failedIndices.size(), 15); // 减少最大重试数量

        // 如果失败章节过多，只重试部分章节
        if (failedIndices.size() > maxRetryCount) {
            log.info("失败章节过多({}个)，只重试前{}个章节", failedIndices.size(), maxRetryCount);
            failedIndices = failedIndices.subList(0, maxRetryCount);
        }

        // 设置重试超时时间
        final long startTime = System.currentTimeMillis();
        final long MAX_RETRY_TIME = 90 * 1000; // 最大重试时间减少到90秒

        // 创建线程池，使用更少的并发以减少被反爬概率
        int parallelism = Math.min(failedIndices.size(), 2); // 最多2个并发
        ExecutorService executor = Executors.newFixedThreadPool(parallelism);
        List<Future<?>> futures = new ArrayList<>();

        // 创建计数器跟踪成功恢复的章节数
        AtomicInteger recoveredCount = new AtomicInteger(0);

        // 提交所有重试任务
        for (Integer index : failedIndices) {
            // 检查是否超时
            if (System.currentTimeMillis() - startTime > MAX_RETRY_TIME) {
                log.warn("重试失败章节超时，已执行{}秒，强制结束重试", (System.currentTimeMillis() - startTime) / 1000);
                break;
            }

            // 提交重试任务
            futures.add(executor.submit(() -> {
                String chapterId = chapterIds.getStr(index);

                try {
                    // 使用递增延迟，避免请求过于集中
                    Thread.sleep(500 + (index % 5) * 200);

                    // 构建请求URL
                    String requestUrl = FANQIE_URL + "?item_id=" + chapterId;

                    // 使用更多的请求头伪装成正常浏览器请求
                    cn.hutool.http.HttpRequest request = cn.hutool.http.HttpUtil.createGet(requestUrl)
                            .header("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1")
                            .header("Accept", "application/json, text/plain, */*")
                            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                            .header("Accept-Encoding", "gzip, deflate, br")
                            .header("Connection", "keep-alive")
                            .header("Referer", "https://fanqienovel.com/")
                            .timeout(10000); // 减少超时时间到10秒

                    // 执行请求
                    cn.hutool.http.HttpResponse response;
                    try {
                        response = request.execute();
                    } catch (Exception e) {
                        return null; // 请求失败，直接返回
                    }

                    if (response.isOk()) {
                        String responseBody = response.body();

                        // 快速解析响应
                        try {
                            JSONObject jsonObject = new JSONObject(responseBody);
                            JSONObject data = jsonObject.getJSONObject("data");
                            String content = data.getStr("content");

                            if (content != null && !content.isEmpty()) {
                                // 获取章节标题
                                String chapterTitle = (chapterTitles != null && index < chapterTitles.size()) ?
                                        chapterTitles.getStr(index) : data.getStr("title", "第" + (index + 1) + "章");

                                // 高效组装章节内容
                                String formattedChapter = chapterTitle + "\r\n\r\n" + content + "\r\n\r\n";

                                // 存储章节内容
                                chapterContents.put(index, formattedChapter);

                                // 从失败集合中移除
                                synchronized (failedChapterIndices) {
                                    failedChapterIndices.remove(index);
                                }

                                // 增加恢复计数
                                recoveredCount.incrementAndGet();
                                return true; // 标记成功
                            }
                        } catch (Exception ignored) {
                            // 解析失败，忽略异常
                        }
                    }
                } catch (Exception ignored) {
                    // 忽略所有异常
                }

                return null; // 标记失败
            }));
        }

        // 等待所有重试任务完成或超时
        try {
            // 设置更短的等待时间
            executor.shutdown();
            if (!executor.awaitTermination(Math.min(MAX_RETRY_TIME - (System.currentTimeMillis() - startTime), 60000), TimeUnit.MILLISECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // 记录重试结果
        log.info("章节重试完成，尝试重试{}章节，成功恢复{}章节，耗时{}秒",
                maxRetryCount, recoveredCount.get(), (System.currentTimeMillis() - startTime) / 1000);
    }

    /**
     * 获取随机User-Agent，减少被反爬的可能性
     * @return 随机User-Agent字符串
     */
    private String getRandomUserAgent() {

        String[] userAgents = {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"
        };

        return userAgents[new Random().nextInt(userAgents.length)];
    }

    /**
     * 检查IP访问频率
     * 如果5小时内同一IP请求超过30次，则封禁该IP 10小时
     * @param ipAddr IP地址
     * @return 状态码
     */
    private String checkIpFrequency(String ipAddr) {
        // 使用Redis作为存储
        String ipCountKey = "ip_request_count:" + ipAddr;
        String ipBlockKey = "ip_blocked:" + ipAddr;

        // 检查IP是否被封禁
        Boolean isBlocked = redisTemplate.hasKey(ipBlockKey);
        if (isBlocked != null && isBlocked) {
            return "540"; // 自定义错误码：IP被临时封禁
        }

        // 增加IP请求计数
        Long count = redisTemplate.opsForValue().increment(ipCountKey, 1);
        if (count == 1) {
            // 第一次请求，设置5小时过期
            redisTemplate.expire(ipCountKey, 5, TimeUnit.HOURS);
        }

        // 如果5小时内请求次数超过30次，封禁IP 10小时
        if (count > 30) {
            redisTemplate.opsForValue().set(ipBlockKey, "blocked", 10, TimeUnit.HOURS);
            log.warn("检测到IP爬虫行为，IP地址: {}, 已临时封禁10小时", ipAddr);
            return "540"; // IP被临时封禁
        }

        return "200"; // IP检查通过
    }

    /**
     * 修改状态
     *
     * @param tokenName
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:update_status')")
    @PostMapping("/update_status")
    public AjaxResult updateStatus(@RequestParam String tokenName, @RequestParam Integer tokenStatus) {
        return tokenService.updateStatus(tokenName,tokenStatus) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 批量修改状态
     *
     * @param tokens
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:batch_update_status')")
    @PostMapping("/batch_update_status")
    public AjaxResult batchUpdateStatus(@RequestBody JSONObject tokens) {
        JSONArray tokenNames = tokens.getJSONArray("tokenNames");
        Integer tokenStatus = tokens.getInt("tokenStatus");
        return tokenService.updateBatchStatus(tokenNames,tokenStatus) > 0 ? AjaxResult.success(tokenService.updateBatchStatus(tokenNames,tokenStatus)) : AjaxResult.error();
    }

    /**
     * 修改额度
     *
     * @param tokenName
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:handle:update_quota')")
    @PostMapping("/update_quota")
    public AjaxResult updateQuota(@RequestParam String tokenName,
                                  @RequestParam Integer baseLimit,
                                  @RequestParam Integer tempNum,
                                  @RequestParam Integer currentUseNum,
                                  @RequestParam Integer permanentQuota
    ) {
        return tokenService.updateQuota(tokenName,baseLimit,tempNum,currentUseNum,permanentQuota) > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 获取当前用户激活码
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:userTokenInfo')")
    @GetMapping("/get_user_token")
    public AjaxResult getUserToken() {
        return success(tokenService.selectFqTokenByUserId(getUserId()));
    }

    /**
     * 获取用户当前额度信息
     * @param tokenName 激活码
     * @param clientVersion 客户端版本
     * @return 额度信息
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:get_quota')")
    @GetMapping("/get_quota")
    public AjaxResult getQuota(
            @RequestParam String tokenName,
            @RequestParam(required = false) String clientVersion,
            HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========用户【{}】查询当前额度信息，客户端版本：{}，IP地址：{}========>",
                tokenName, clientVersion, ipAddr);

        // 验证客户端版本
        if (clientVersion != null && !clientVersion.isEmpty()) {
            FqVersion latestVersion = versionService.checkUpdate(clientVersion, "All", "All");
            if (latestVersion != null && latestVersion.getIsMandatory() == 1) {
                // 需要强制更新，不允许使用
                log.warn("<==========用户【{}】查询额度失败：客户端版本过低，需要强制更新========>", tokenName);
                return AjaxResult.error("客户端版本过低，请更新到最新版本");
            }
        }

        Map<String, Object> quotaInfo = tokenService.getCurrentQuota(tokenName);

        if ("error".equals(quotaInfo.get("status"))) {
            log.warn("<==========用户【{}】查询额度失败：{}========>", tokenName, quotaInfo.get("message"));
            return AjaxResult.error(quotaInfo.get("message").toString());
        }

        log.info("<==========用户【{}】查询额度成功，基础额度：{}，活动额度：{}，总额度：{}，已使用：{}，剩余：{}========>",
                tokenName, quotaInfo.get("baseLimit"), quotaInfo.get("activityLimit"),
                quotaInfo.get("totalLimit"), quotaInfo.get("currentUseNum"), quotaInfo.get("remainingLimit"));

        return AjaxResult.success(quotaInfo);
    }

    /**
     * 一键清空所有用户的当前使用次数
     * @param request 请求对象，用于获取操作者IP
     * @return 清空结果
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:reset_all_current_use_num')")
    @PostMapping("/reset_all_current_use_num")
    public AjaxResult resetAllCurrentUseNum(HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========管理员【{}】执行一键清空所有用户当前使用次数操作========>", ipAddr);

        int count = tokenService.resetAllCurrentUseNum();

        log.info("<==========一键清空所有用户当前使用次数操作完成，共重置{}个用户=========>", count);
        return count > 0 ? AjaxResult.success(count) : AjaxResult.error("没有用户需要重置");
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:batch_delete')")
    @DeleteMapping("/delete/batch")
    public AjaxResult deleteByIds(@RequestBody JSONObject ids) {
        List<Integer> list = ids.getJSONArray("ids").toList(Integer.class);
        if (list!=null) {
            for (Integer id : list){
                tokenService.delete(id);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 批量导出数据
     */
    @GetMapping("/export")
    public void exportData(HttpServletResponse response) throws IOException {
        ExcelWriter writer = ExcelUtil.getWriter(true);

        //全部导出
        List<FqToken> list = tokenService.selectAll();

        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList();
        for (FqToken token : list) {
            Map<String, Object> row = new LinkedHashMap<>();

            row.put("序号", token.getTokenId());
            row.put("激活码", token.getTokenName());
            if(token.getTokenCompCode()==null){
                row.put("激活情况", "未激活");
            }else {
                row.put("激活情况", "已激活");
            }
            row.put("创建时间", token.getTokenTime());
            row.put("总使用次数", token.getUseNum());
            row.put("每日使用额度", token.getUseLimit());
            row.put("当前使用次数", token.getCurrentUseNum());

            rows.add(row);
        }


        //设置学号宽度
        writer.getSheet().setColumnWidth(0, 3000);
        //设置激活码宽度
        writer.getSheet().setColumnWidth(1, 12000);
        //设置创建时间宽度
        writer.getSheet().setColumnWidth(3, 6000);

        writer.write(rows, true);


        //设置输出格式
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("激活码信息表", "UTF-8") + ".xlsx");

        ServletOutputStream outputStream = response.getOutputStream();
        writer.flush(outputStream, true);
        writer.close();
        outputStream.flush();
        outputStream.close();
    }

    /**
     * 定时清理过期任务
     * 每小时执行一次，清理超过8小时的任务
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 每小时执行一次
    public void cleanExpiredTasks() {
        log.info("开始清理过期下载任务...");
        int cleanedCount = 0;

        try {
            // 获取所有下载任务
            Set<String> taskKeys = redisTemplate.keys(REDIS_TASK_KEY_PREFIX + "*");
            if (taskKeys == null || taskKeys.isEmpty()) {
                log.info("没有找到需要清理的任务");
                return;
            }

            Date now = new Date();
            for (String key : taskKeys) {
                try {
                    // 获取任务创建时间
                    Object createTimeObj = redisTemplate.opsForHash().get(key, "createTime");
                    if (createTimeObj == null) continue;

                    Date createTime;
                    if (createTimeObj instanceof Date) {
                        createTime = (Date) createTimeObj;
                    } else {
                        // 尝试解析字符串格式的日期
                        try {
                            createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createTimeObj.toString());
                        } catch (Exception e) {
                            log.warn("无法解析任务创建时间: {}", createTimeObj);
                            continue;
                        }
                    }

                    // 计算任务已存在的时间（小时）
                    long taskAgeHours = (now.getTime() - createTime.getTime()) / (60 * 60 * 1000);

                    // 如果任务超过8小时，或者任务状态为COMPLETED/FAILED且超过2小时，则清理
                    String status = (String) redisTemplate.opsForHash().get(key, "status");
                    boolean isCompleted = TASK_STATUS_COMPLETED.equals(status) || TASK_STATUS_FAILED.equals(status);

                    if (taskAgeHours >= TASK_EXPIRE_HOURS || (isCompleted && taskAgeHours >= 2)) {
                        // 获取任务ID和小说ID，用于清理相关锁
                        String taskId = (String) redisTemplate.opsForHash().get(key, "taskId");
                        String novelId = (String) redisTemplate.opsForHash().get(key, "novelId");

                        // 清理小说下载锁
                        if (novelId != null) {
                            String novelTaskKey = "novel_downloading:" + novelId;
                            String lockTaskId = (String) redisTemplate.opsForValue().get(novelTaskKey);

                            // 只有当锁对应的任务ID与当前任务ID匹配时才删除锁
                            if (taskId != null && taskId.equals(lockTaskId)) {
                                redisTemplate.delete(novelTaskKey);
                                log.info("清理小说下载锁: {}", novelTaskKey);
                            }
                        }

                        // 删除任务
                        redisTemplate.delete(key);
                        cleanedCount++;

                        log.info("清理过期任务: {}, 状态: {}, 存在时间: {}小时", key, status, taskAgeHours);
                    }
                } catch (Exception e) {
                    log.error("清理任务{}时出错: {}", key, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("清理过期任务时发生错误: {}", e.getMessage(), e);
        }

        log.info("过期任务清理完成，共清理{}个任务", cleanedCount);
    }

    /**
     * 清除当前所有下载任务
     * @param request 请求对象，用于获取操作者IP
     * @return 清除结果
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:clear_all_download_tasks')")
    @PostMapping("/clear_all_download_tasks")
    public AjaxResult clearAllDownloadTasks(HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========管理员【{}】执行清除所有下载任务操作========>", ipAddr);

        int cleanedCount = 0;
        try {
            // 获取所有下载任务
            Set<String> taskKeys = redisTemplate.keys(REDIS_TASK_KEY_PREFIX + "*");
            if (taskKeys == null || taskKeys.isEmpty()) {
                log.info("没有找到需要清理的任务");
                return AjaxResult.success("没有找到需要清理的任务");
            }

            for (String key : taskKeys) {
                try {
                    // 获取任务信息用于日志记录
                    String taskId = (String) redisTemplate.opsForHash().get(key, "taskId");
                    String novelId = (String) redisTemplate.opsForHash().get(key, "novelId");
                    String status = (String) redisTemplate.opsForHash().get(key, "status");

                    // 清理小说下载锁
                    if (novelId != null) {
                        String novelTaskKey = "novel_downloading:" + novelId;
                        String lockTaskId = (String) redisTemplate.opsForValue().get(novelTaskKey);

                        // 只有当锁对应的任务ID与当前任务ID匹配时才删除锁
                        if (taskId != null && taskId.equals(lockTaskId)) {
                            redisTemplate.delete(novelTaskKey);
                            log.info("清理小说下载锁: {}", novelTaskKey);
                        }
                    }

                    // 删除任务
                    redisTemplate.delete(key);
                    cleanedCount++;

                    log.info("清理下载任务: {}, 状态: {}", taskId, status);
                } catch (Exception e) {
                    log.error("清理任务{}时出错: {}", key, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("清理下载任务时发生错误: {}", e.getMessage(), e);
            return AjaxResult.error("清理下载任务时发生错误: " + e.getMessage());
        }

        log.info("<==========下载任务清理完成，共清理{}个任务=========>", cleanedCount);
        return AjaxResult.success("成功清理" + cleanedCount + "个下载任务");
    }

    /**
     * 删除指定任务ID的下载任务
     * @param taskId 任务ID
     * @param request 请求对象，用于获取操作者IP
     * @return 删除结果
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:delete_download_task')")
    @DeleteMapping("/delete_download_task")
    public AjaxResult deleteDownloadTask(@RequestParam String taskId, HttpServletRequest request) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.info("<==========管理员【{}】执行删除指定下载任务操作，任务ID: {}========>", ipAddr, taskId);

        if (taskId == null || taskId.trim().isEmpty()) {
            return AjaxResult.error("任务ID不能为空");
        }

        try {
            // 构建任务键
            String redisKey = REDIS_TASK_KEY_PREFIX + taskId;

            // 检查任务是否存在
            if (!redisTemplate.hasKey(redisKey)) {
                log.warn("<==========任务【{}】不存在或已过期========>", taskId);
                return AjaxResult.error("任务不存在或已过期");
            }

            // 获取任务信息用于清理锁
            String novelId = (String) redisTemplate.opsForHash().get(redisKey, "novelId");
            String status = (String) redisTemplate.opsForHash().get(redisKey, "status");

            // 清理小说下载锁
            if (novelId != null) {
                String novelTaskKey = "novel_downloading:" + novelId;
                String lockTaskId = (String) redisTemplate.opsForValue().get(novelTaskKey);

                // 只有当锁对应的任务ID与当前任务ID匹配时才删除锁
                if (taskId.equals(lockTaskId)) {
                    redisTemplate.delete(novelTaskKey);
                    log.info("清理小说下载锁: {}", novelTaskKey);
                }
            }

            // 删除任务
            redisTemplate.delete(redisKey);

            log.info("<==========成功删除任务【{}】，状态: {}========>", taskId, status);
            return AjaxResult.success("成功删除任务");

        } catch (Exception e) {
            log.error("<==========删除任务【{}】时发生错误: {}========>", taskId, e.getMessage(), e);
            return AjaxResult.error("删除任务时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取当前所有下载任务
     * @return 所有下载任务列表
     */
    @PreAuthorize("@ss.hasPermi('fq_token:token:get_all_download_tasks')")
    @GetMapping("/get_all_download_tasks")
    public AjaxResult getAllDownloadTasks() {
        log.info("<==========管理员【{}】获取所有下载任务列表========>", getUsername());

        try {
            // 获取所有下载任务的键
            Set<String> taskKeys = redisTemplate.keys(REDIS_TASK_KEY_PREFIX + "*");
            if (taskKeys == null || taskKeys.isEmpty()) {
                log.info("当前没有进行中的下载任务");
                return AjaxResult.success(new ArrayList<>());
            }

            // 存储所有任务信息
            List<Map<Object, Object>> tasksList = new ArrayList<>();

            // 遍历所有任务键，获取任务信息
            for (String key : taskKeys) {
                try {
                    Map<Object, Object> taskInfo = redisTemplate.opsForHash().entries(key);

                    // 添加任务键名（不包含前缀）到任务信息中
                    String taskId = key.substring(REDIS_TASK_KEY_PREFIX.length());
                    if (!taskInfo.containsKey("taskId")) {
                        taskInfo.put("taskId", taskId);
                    }

                    // 格式化日期字段，确保前端能正确显示
                    if (taskInfo.containsKey("createTime") && taskInfo.get("createTime") instanceof Date) {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        taskInfo.put("createTimeStr", dateFormat.format(taskInfo.get("createTime")));
                    }

                    if (taskInfo.containsKey("updateTime") && taskInfo.get("updateTime") instanceof Date) {
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        taskInfo.put("updateTimeStr", dateFormat.format(taskInfo.get("updateTime")));
                    }

                    // 计算任务持续时间
                    if (taskInfo.containsKey("createTime")) {
                        Date createTime = (Date) taskInfo.get("createTime");
                        Date updateTime = taskInfo.containsKey("updateTime") ?
                                (Date) taskInfo.get("updateTime") : new Date();

                        long durationMillis = updateTime.getTime() - createTime.getTime();
                        long minutes = durationMillis / (60 * 1000);
                        long seconds = (durationMillis % (60 * 1000)) / 1000;

                        taskInfo.put("duration", minutes + "分" + seconds + "秒");
                    }

                    tasksList.add(taskInfo);
                } catch (Exception e) {
                    log.error("获取任务{}信息时出错: {}", key, e.getMessage(), e);
                }
            }

            // 按更新时间倒序排序，最新的任务排在前面
            tasksList.sort((task1, task2) -> {
                Date updateTime1 = task1.containsKey("updateTime") ? (Date) task1.get("updateTime") : null;
                Date updateTime2 = task2.containsKey("updateTime") ? (Date) task2.get("updateTime") : null;

                if (updateTime1 == null && updateTime2 == null) return 0;
                if (updateTime1 == null) return 1;
                if (updateTime2 == null) return -1;

                // 倒序排列，最新的在前面
                return updateTime2.compareTo(updateTime1);
            });

            log.info("<==========成功获取{}个下载任务信息========>", tasksList.size());
            return AjaxResult.success(tasksList);

        } catch (Exception e) {
            log.error("<==========获取下载任务列表时发生错误: {}========>", e.getMessage(), e);
            return AjaxResult.error("获取下载任务列表时发生错误: " + e.getMessage());
        }
    }

}
