from astrbot.api.event import filter, AstrMessageEvent, MessageEventResult
from astrbot.api.star import Context, <PERSON>, register
from astrbot.api.message_components import *
from astrbot.api.event.filter import command, command_group
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.schedulers.base import JobLookupError
from astrbot.api.event import MessageChain
import datetime
import json
import os
import aiohttp
import random
import logging

# 获取logger实例
logger = logging.getLogger(__name__)

@register("english_sentence_plugin", "english_sentence", "英语金句插件", "1.0.0")  # 注册为英语金句插件
class EnglishSentencePlugin(Star):
    def __init__(self, context: Context):
        super().__init__(context)
        self.scheduler = AsyncIOScheduler()
        # 使用插件目录下的数据文件
        plugin_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_file = os.path.join(plugin_dir, "english_sentence.json")
        # 初始化数据存储
        if not os.path.exists(self.data_file):
            with open(self.data_file, "w", encoding='utf-8') as f:
                f.write("{}")
        with open(self.data_file, "r", encoding='utf-8') as f:
            self.sentence_data = json.load(f)

        # API配置
        self.api_url = "https://dify.pg-code-go.com/v1/completion-messages"
        self.api_key = "app-ZDE3Khy2p0FQa8MF4FeeaNjH"

        self._init_scheduler()
        self.scheduler.start()

    def _init_scheduler(self):
        '''初始化定时器，加载英语金句任务'''
        if "sentence_schedules" not in self.sentence_data:
            self.sentence_data["sentence_schedules"] = {}

        # 加载英语金句定时任务
        for group in self.sentence_data["sentence_schedules"]:
            sentence_schedule = self.sentence_data["sentence_schedules"][group]
            if "time" in sentence_schedule:
                dt = datetime.datetime.strptime(sentence_schedule["time"], "%H:%M")
                job_id = f"sentence_{group}"
                self.scheduler.add_job(
                    self._sentence_callback,
                    'cron',
                    args=[group],
                    hour=dt.hour,
                    minute=dt.minute,
                    misfire_grace_time=60,
                    id=job_id
                )

    async def _save_data(self):
        '''保存英语金句数据'''
        with open(self.data_file, "w", encoding='utf-8') as f:
            # 只保存 sentence_schedules 部分
            if "sentence_schedules" in self.sentence_data:
                json.dump({"sentence_schedules": self.sentence_data["sentence_schedules"]}, f, ensure_ascii=False, indent=4)
            else:
                json.dump({"sentence_schedules": {}}, f, ensure_ascii=False, indent=4) # 确保即使没有数据也写入空结构

    async def get_sentence_from_api(self, api_key=None):
        """
        从API获取英文句子和翻译

        参数:
            api_key: API密钥，如果为None则使用全局配置或环境变量

        返回:
            tuple: (英文句子, 中文翻译) 或 None（如果API调用失败）
        """
        try:
            # 获取API密钥
            if api_key is None:
                api_key = self.api_key or os.getenv('DIFY_API_KEY')

            if not api_key:
                logger.warning("未设置API密钥，请设置API_KEY变量或DIFY_API_KEY环境变量")
                return None

            # API请求配置
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }
            data = {
                "inputs": {"query": "Hello, world!"},
                "response_mode": "blocking",
                "user": "pg-code"
            }

            logger.info("正在从API获取英文句子和翻译...")

            # 发送POST请求
            async with aiohttp.ClientSession() as session:
                async with session.post(self.api_url, headers=headers, json=data, timeout=10) as response:
                    response.raise_for_status()  # 检查HTTP错误

                    # 解析JSON响应
                    result = await response.json()

                    # 提取answer字段
                    if "answer" in result:
                        answer = result["answer"].strip()
                        logger.info(f"API响应: {answer}")

                        # 解析英文和翻译
                        # 预期格式: "英文：xxx  \n翻译：yyy"
                        lines = answer.split('\n')
                        english_line = None
                        chinese_line = None

                        for line in lines:
                            line = line.strip()
                            if line.startswith("英文："):
                                english_line = line
                            elif line.startswith("翻译："):
                                chinese_line = line

                        if english_line and chinese_line:
                            english = english_line.replace("英文：", "").strip()
                            chinese = chinese_line.replace("翻译：", "").strip()

                            if english and chinese:
                                logger.info(f"成功解析 - 英文: {english[:50]}...")
                                logger.info(f"成功解析 - 翻译: {chinese[:50]}...")
                                return english, chinese
                            else:
                                logger.warning("解析后的英文或翻译为空")
                                return None
                        else:
                            logger.warning(f"无法从API响应中找到英文和翻译: {answer}")
                            return None
                    else:
                        logger.warning(f"API响应中没有answer字段: {result}")
                        return None

        except aiohttp.ClientTimeout:
            logger.warning("API请求超时")
            return None
        except aiohttp.ClientError as e:
            logger.warning(f"API请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取句子时出现未知错误: {e}")
            return None

    def get_backup_sentence(self):
        """
        获取备用句子（当API不可用时使用）

        返回:
            tuple: (英文句子, 中文翻译)
        """
        backup_sentences = [
            (
                "It is no easy task to identify the reasons for this phenomenon which involves several complicated factors.",
                "要找出这一现象的原因并非易事，因为它涉及若干复杂的因素。"
            ),
            (
                "The rapid development of artificial intelligence has brought about tremendous changes in various fields.",
                "人工智能的快速发展给各个领域带来了巨大的变化。"
            ),
            (
                "Success is not final, failure is not fatal: it is the courage to continue that counts.",
                "成功不是终点，失败不是致命的：重要的是继续前进的勇气。"
            ),
            (
                "While some people believe that technology will solve all our problems, others argue that it may create new challenges.",
                "虽然有些人认为技术将解决我们所有的问题，但其他人则认为，技术可能会创造出新的挑战。"
            ),
            (
                "Education is the most powerful weapon which you can use to change the world.",
                "教育是你可以用来改变世界的最强大的武器。"
            )
        ]

        selected = random.choice(backup_sentences)
        logger.info(f"使用备用句子: {selected[0][:50]}...")
        return selected

    async def _sentence_callback(self, unified_msg_origin: str):
        '''英语金句回调函数'''
        try:
            # 获取英语句子和翻译
            api_result = await self.get_sentence_from_api()
            if api_result:
                english, chinese = api_result
            else:
                # API失败时使用备用句子
                english, chinese = self.get_backup_sentence()

            # 发送文字版本
            msg = MessageChain([
                Plain(f"📚 每日英语金句\n\n💬 英文：{english}\n\n🔤 翻译：{chinese}")
            ])
            await self.context.send_message(unified_msg_origin, msg)
            logger.info(f"英语金句发送成功 to {unified_msg_origin}")

        except Exception as e:
            msg = MessageChain([Plain(f"发送英语金句失败，未知错误: {str(e)}")])
            await self.context.send_message(unified_msg_origin, msg)
            logger.error(f"发送英语金句失败 for {unified_msg_origin}: 未知错误 {str(e)}")

    @command_group("sentence") # 顶层命令组改为 "sentence"
    def sentence_group(self):
        '''英语金句相关命令'''
        pass

    @sentence_group.command("set")
    async def set_sentence(self, event: AstrMessageEvent, time_str: str):
        '''设置每日英语金句发送时间'''
        try:
            datetime.datetime.strptime(time_str, "%H:%M")
            msg_origin = event.unified_msg_origin
            if "sentence_schedules" not in self.sentence_data:
                self.sentence_data["sentence_schedules"] = {}
            self.sentence_data["sentence_schedules"][msg_origin] = {"time": time_str}
            await self._save_data()

            # 移除旧的英语金句任务 (如果存在)
            old_job_id = f"sentence_{msg_origin}"
            try:
                self.scheduler.remove_job(old_job_id)
            except JobLookupError:
                pass

            # 添加新的英语金句任务
            dt = datetime.datetime.strptime(time_str, "%H:%M")
            job_id = f"sentence_{msg_origin}"
            self.scheduler.add_job(
                self._sentence_callback,
                'cron',
                args=[msg_origin],
                hour=dt.hour,
                minute=dt.minute,
                misfire_grace_time=60,
                id=job_id
            )
            yield event.plain_result(f"每日英语金句已设置为每天 {time_str} 发送。")
        except ValueError:
            yield event.plain_result("时间格式错误，请使用 HH:MM 格式（如 08:05）。")
        except Exception as e:
            yield event.plain_result(f"设置英语金句时间出错：{str(e)}")

    @sentence_group.command("unset")
    async def unset_sentence(self, event: AstrMessageEvent):
        '''取消每日英语金句'''
        msg_origin = event.unified_msg_origin
        if "sentence_schedules" in self.sentence_data and msg_origin in self.sentence_data["sentence_schedules"]:
            del self.sentence_data["sentence_schedules"][msg_origin]
            await self._save_data()
            # 移除调度任务
            job_id = f"sentence_{msg_origin}"
            try:
                self.scheduler.remove_job(job_id)
                logger.info(f"Successfully removed sentence job: {job_id}")
            except JobLookupError:
                logger.error(f"Sentence job not found: {job_id}")
            yield event.plain_result("已取消每日英语金句。")
        else:
            yield event.plain_result("每日英语金句未设置。")

    @sentence_group.command("status")
    async def sentence_status(self, event: AstrMessageEvent):
        '''查看英语金句设置状态'''
        msg_origin = event.unified_msg_origin
        if "sentence_schedules" in self.sentence_data and msg_origin in self.sentence_data["sentence_schedules"]:
            time_str = self.sentence_data["sentence_schedules"][msg_origin]["time"]
            yield event.plain_result(f"每日英语金句已设置为每天 {time_str} 发送。")
        else:
            yield event.plain_result("每日英语金句未设置。")

    @sentence_group.command("now")
    async def sentence_now(self, event: AstrMessageEvent):
        '''立即获取一条英语金句'''
        try:
            # 获取英语句子和翻译
            api_result = await self.get_sentence_from_api()
            if api_result:
                english, chinese = api_result
            else:
                # API失败时使用备用句子
                english, chinese = self.get_backup_sentence()

            # 直接发送文字版本
            yield event.plain_result(f"📚 英语金句\n\n💬 英文：{english}\n\n🔤 翻译：{chinese}")

        except Exception as e:
            yield event.plain_result(f"获取英语金句失败：{str(e)}")

    @sentence_group.command("help")
    async def show_help(self, event: AstrMessageEvent):
        '''显示英语金句帮助信息'''
        help_text = """英语金句插件指令说明：
1. 设置每日英语金句:
   /sentence set <时间> - 设置每日英语金句发送时间 (HH:MM)
2. 取消每日英语金句:
   /sentence unset - 取消每日英语金句
3. 查看金句设置:
   /sentence status - 查看英语金句设置状态
4. 立即获取金句:
   /sentence now - 立即获取一条英语金句

注：时间格式为 HH:MM，如 8:05 或 08:05"""
        yield event.plain_result(help_text)