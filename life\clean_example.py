#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英语句子图片生成器 - 简洁版本使用示例
去除了英文和中文翻译的红色标题部分，内容更加简洁优雅
"""

from english_sentence import generate_sentence_image

def generate_clean_examples():
    """生成简洁版本的示例图片"""
    print("🎨 生成简洁版本的英语句子图片")
    print("=" * 50)
    
    # 示例句子数据
    examples = [
        {
            "data": [
                "英文：The only impossible journey is the one you never begin.",
                "翻译：唯一不可能的旅程是你从未开始的那一个。"
            ],
            "filename": "clean_example_journey.png",
            "theme": "默认红色",
            "color": None
        },
        {
            "data": [
                "英文：Success is not the key to happiness. Happiness is the key to success.",
                "翻译：成功不是幸福的关键，幸福才是成功的关键。"
            ],
            "filename": "clean_example_success.png",
            "theme": "清新绿色",
            "color": (67, 160, 71)
        },
        {
            "data": [
                "英文：The future depends on what you do today.",
                "翻译：未来取决于你今天所做的事情。"
            ],
            "filename": "clean_example_future.png",
            "theme": "专业蓝色",
            "color": (33, 150, 243)
        },
        {
            "data": [
                "英文：Education is the most powerful weapon which you can use to change the world.",
                "翻译：教育是你可以用来改变世界的最强大的武器。"
            ],
            "filename": "clean_example_education.png",
            "theme": "神秘紫色",
            "color": (156, 39, 176)
        },
        {
            "data": [
                "英文：The best time to plant a tree was 20 years ago. The second best time is now.",
                "翻译：种一棵树最好的时间是20年前，其次是现在。"
            ],
            "filename": "clean_example_tree.png",
            "theme": "温暖橙色",
            "color": (255, 138, 101)
        }
    ]
    
    generated_files = []
    
    for i, example in enumerate(examples, 1):
        print(f"\n📝 生成示例 {i}: {example['theme']}主题")
        print(f"   句子: {example['data'][0][:50]}...")
        
        try:
            if example['color']:
                output_path = generate_sentence_image(
                    example['data'], 
                    example['filename'],
                    bg_color=example['color']
                )
            else:
                output_path = generate_sentence_image(
                    example['data'], 
                    example['filename']
                )
            
            generated_files.append({
                'filename': example['filename'],
                'theme': example['theme'],
                'path': output_path
            })
            print(f"   ✅ 生成成功: {output_path}")
            
        except Exception as e:
            print(f"   ❌ 生成失败: {e}")
    
    return generated_files

def main():
    """主函数"""
    print("🌟 英语句子图片生成器 - 简洁版本演示")
    print("去除了英文和中文翻译的红色标题部分")
    print("让内容更加简洁优雅，提升阅读体验")
    print()
    
    try:
        generated_files = generate_clean_examples()
        
        print("\n" + "=" * 50)
        print("✅ 所有简洁版本示例图片生成完成！")
        print(f"\n📁 共生成 {len(generated_files)} 张图片：")
        
        for i, file_info in enumerate(generated_files, 1):
            print(f"   {i:2d}. {file_info['filename']} ({file_info['theme']})")
        
        print("\n🎯 简洁版本的主要特点：")
        print("   • 去除了'English'和'中文翻译'的红色标题")
        print("   • 使用统一的深灰色文字，视觉更协调")
        print("   • 简化了装饰分割线，更加优雅")
        print("   • 重新优化了布局间距，更加紧凑")
        print("   • 保持了多种主题色彩选择")
        
        print("\n💡 使用建议：")
        print("   • 适合需要简洁设计的场景")
        print("   • 更适合专业的教育和学习环境")
        print("   • 减少视觉干扰，提升阅读专注度")
        print("   • 保持了原有的美观性和功能性")
        
    except Exception as e:
        print(f"❌ 生成过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
