# API配置示例文件
# 复制此文件为 api_config.py 并设置你的API密钥

# Dify API配置
DIFY_API_KEY = "app-your-api-key-here"  # 替换为你的实际API密钥
DIFY_API_URL = "https://dify.pg-code-go.com/v1/completion-messages"

# 使用说明：
# 1. 将此文件复制为 api_config.py
# 2. 替换 DIFY_API_KEY 为你的实际API密钥
# 3. 在 english_sentence.py 中导入配置：
#    from api_config import DIFY_API_KEY
#    API_KEY = DIFY_API_KEY

# 或者，你可以设置环境变量：
# Windows: set DIFY_API_KEY=app-your-api-key-here
# Linux/Mac: export DIFY_API_KEY=app-your-api-key-here

# API密钥获取方法：
# 1. 访问 Dify 平台
# 2. 创建或选择一个应用
# 3. 在应用设置中找到API密钥
# 4. 复制API密钥并替换上面的占位符
