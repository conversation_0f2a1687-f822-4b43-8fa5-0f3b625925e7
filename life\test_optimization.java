import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

/**
 * 测试章节信息数组优化的示例代码
 */
public class TestOptimization {
    
    /**
     * 测试章节信息数组的创建和使用
     */
    public static void testChapterInfoArray() {
        // 模拟原始数据
        JSONArray chapterIds = new JSONArray();
        chapterIds.add("chapter_001");
        chapterIds.add("chapter_002");
        chapterIds.add("chapter_003");
        
        JSONArray chapterTitles = new JSONArray();
        chapterTitles.add("第一章 开始");
        chapterTitles.add("第二章 冒险");
        chapterTitles.add("第三章 结束");
        
        // 创建章节信息数组（优化后的方式）
        JSONArray chapterInfoArray = new JSONArray();
        int totalChapters = chapterIds.size();
        
        for (int i = 0; i < totalChapters; i++) {
            JSONObject chapterInfo = new JSONObject();
            chapterInfo.put("id", chapterIds.getStr(i));
            
            // 获取章节标题，如果标题数组存在且索引有效则使用，否则使用默认标题
            String chapterTitle = (chapterTitles != null && i < chapterTitles.size())
                ? chapterTitles.getStr(i)
                : "第" + (i + 1) + "章";
            chapterInfo.put("title", chapterTitle);
            chapterInfo.put("index", i); // 添加索引，确保按顺序组装
            
            chapterInfoArray.add(chapterInfo);
        }
        
        // 验证结果
        System.out.println("章节信息数组创建成功:");
        for (int i = 0; i < chapterInfoArray.size(); i++) {
            JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
            System.out.println(String.format("索引: %d, ID: %s, 标题: %s", 
                chapterInfo.getInt("index"),
                chapterInfo.getStr("id"),
                chapterInfo.getStr("title")));
        }
    }
    
    /**
     * 测试章节标题获取逻辑
     */
    public static void testChapterTitleRetrieval() {
        // 创建测试用的章节信息数组
        JSONArray chapterInfoArray = new JSONArray();
        
        JSONObject chapter1 = new JSONObject();
        chapter1.put("id", "chapter_001");
        chapter1.put("title", "第一章 测试标题");
        chapter1.put("index", 0);
        chapterInfoArray.add(chapter1);
        
        JSONObject chapter2 = new JSONObject();
        chapter2.put("id", "chapter_002");
        chapter2.put("title", "第二章 另一个标题");
        chapter2.put("index", 1);
        chapterInfoArray.add(chapter2);
        
        // 模拟从响应中获取标题的情况
        String responseTitle = "响应中的标题";
        int chapterIndex = 0;
        
        // 优先使用响应中的标题
        String finalTitle = responseTitle;
        if (finalTitle == null || finalTitle.isEmpty()) {
            // 如果响应中的标题为空，从章节信息数组中获取
            if (chapterIndex < chapterInfoArray.size()) {
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
                finalTitle = chapterInfo.getStr("title");
            } else {
                finalTitle = "第" + (chapterIndex + 1) + "章";
            }
        }
        
        System.out.println("标题获取测试:");
        System.out.println("响应标题: " + responseTitle);
        System.out.println("预设标题: " + chapterInfoArray.getJSONObject(chapterIndex).getStr("title"));
        System.out.println("最终标题: " + finalTitle);
        
        // 测试响应标题为空的情况
        responseTitle = null;
        finalTitle = responseTitle;
        if (finalTitle == null || finalTitle.isEmpty()) {
            if (chapterIndex < chapterInfoArray.size()) {
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(chapterIndex);
                finalTitle = chapterInfo.getStr("title");
            } else {
                finalTitle = "第" + (chapterIndex + 1) + "章";
            }
        }
        
        System.out.println("\n响应标题为空时:");
        System.out.println("响应标题: " + responseTitle);
        System.out.println("最终标题: " + finalTitle);
    }
    
    /**
     * 测试章节顺序组装
     */
    public static void testChapterOrdering() {
        // 创建章节信息数组
        JSONArray chapterInfoArray = new JSONArray();
        for (int i = 0; i < 5; i++) {
            JSONObject chapterInfo = new JSONObject();
            chapterInfo.put("id", "chapter_" + String.format("%03d", i + 1));
            chapterInfo.put("title", "第" + (i + 1) + "章");
            chapterInfo.put("index", i);
            chapterInfoArray.add(chapterInfo);
        }
        
        // 模拟章节内容（部分章节可能获取失败）
        String[] chapterContents = {
            "第1章内容",
            null, // 第2章获取失败
            "第3章内容",
            null, // 第4章获取失败
            "第5章内容"
        };
        
        // 按顺序组装小说内容
        StringBuilder novelContent = new StringBuilder();
        novelContent.append("书名：测试小说\r\n\r\n");
        novelContent.append("====================\r\n\r\n");
        
        for (int i = 0; i < chapterInfoArray.size(); i++) {
            String content = chapterContents[i];
            
            if (content != null) {
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
                String chapterTitle = chapterInfo.getStr("title");
                novelContent.append(chapterTitle).append("\r\n\r\n");
                novelContent.append(content).append("\r\n\r\n");
            } else {
                // 对于缺失的章节，从章节信息数组中获取标题并添加占位符
                JSONObject chapterInfo = chapterInfoArray.getJSONObject(i);
                String chapterTitle = chapterInfo.getStr("title");
                novelContent.append(chapterTitle).append("\r\n\r\n");
                novelContent.append("[章节内容获取失败，请稍后重试]\r\n\r\n");
            }
        }
        
        System.out.println("章节顺序组装测试:");
        System.out.println(novelContent.toString());
    }
    
    public static void main(String[] args) {
        System.out.println("=== 章节信息数组优化测试 ===\n");
        
        testChapterInfoArray();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testChapterTitleRetrieval();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testChapterOrdering();
    }
}
